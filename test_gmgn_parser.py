#!/usr/bin/env python3
"""
Test script for GMGN Parser
Run this to verify the GMGN parser is working correctly
"""

from gmgn_parser import gmgn_message_parser, get_gmgn_parser
import json

def test_gmgn_parser():
    """Test the GMGN parser with sample messages"""
    
    # Sample GMGN messages for testing
    test_messages = [
        # KOL Buy example
        """🔥 KOL Buy Alert! 
$PEPE (Pepe Token)
CA: 7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr
MCP: $45.2K
Liq: SOL/USDC ($12.5K)
Holder: 156
Open: 2.3h ago
https://gmgn.ai/sol/token/7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr""",

        # Smart Money example
        """💰 Smart Money FOMO!
$MOON (Moon Shot)
Contract: 4vMsoUT2BWatFweudnQM1xedRLfJgJ7hswhcpz4xgBTy
MCP: $123.8K
Liq: SOL/USDC ($45.2K)
Holder: 89
Open: 45m ago
Price: $0.00123 (+156.7%)""",

        # FDV Surge example
        """📈 FDV Surge Alert!
$ROCKET (Rocket Token)
https://gmgn.ai/sol/address/9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM
MCP: $89.5K
Holder: 234
Open: 1.2h ago""",

        # Non-GMGN message (should be ignored)
        """Just a regular message with no token info
This should not trigger any alerts
Random text here""",

        # Multiple CAs in one message
        """🚀 Multiple Alerts!
$TOKEN1: 5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1
$TOKEN2: 8HvwTakjE2bzQ4M1x2YdmtAUy6y9PyUvMLhLwqZcUBVv
Both showing strong signals!"""
    ]
    
    print("🧪 Testing GMGN Parser...")
    print("=" * 50)
    
    parser = get_gmgn_parser()
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n📝 Test Message {i}:")
        print("-" * 30)
        print(message[:100] + "..." if len(message) > 100 else message)
        print("-" * 30)
        
        # Parse the message
        results = parser.parse_message(message)
        
        if results:
            print(f"✅ Found {len(results)} token alert(s):")
            for j, result in enumerate(results, 1):
                print(f"  Alert {j}:")
                print(f"    CA: {result['ca']}")
                print(f"    Type: {result['alert_type']}")
                print(f"    Signal Strength: {result['signal_strength']:.2f}")
                print(f"    Metadata: {json.dumps(result['metadata'], indent=6)}")
        else:
            print("❌ No alerts detected")
    
    # Print parser statistics
    print("\n📊 Parser Statistics:")
    print("=" * 50)
    stats = parser.get_stats()
    print(json.dumps(stats, indent=2))

def test_individual_functions():
    """Test individual parser functions"""
    from gmgn_parser import extract_contract_addresses, detect_alert_type, parse_token_metadata
    
    print("\n🔧 Testing Individual Functions:")
    print("=" * 50)
    
    test_message = """🔥 KOL Buy Alert! 
$PEPE (Pepe Token)
CA: 7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr
MCP: $45.2K
Liq: SOL/USDC ($12.5K)
Holder: 156
Open: 2.3h ago"""
    
    print("Test Message:")
    print(test_message)
    print()
    
    # Test CA extraction
    cas = extract_contract_addresses(test_message)
    print(f"Contract Addresses: {cas}")
    
    # Test alert type detection
    alert_type = detect_alert_type(test_message)
    print(f"Alert Type: {alert_type}")
    
    # Test metadata parsing
    metadata = parse_token_metadata(test_message)
    print(f"Metadata: {json.dumps(metadata, indent=2)}")

if __name__ == "__main__":
    try:
        test_gmgn_parser()
        test_individual_functions()
        print("\n✅ All tests completed!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
