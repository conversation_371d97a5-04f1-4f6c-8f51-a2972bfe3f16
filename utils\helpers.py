"""
Monaco v1.0 - Utility Functions
Comprehensive helper functions for Solana RPC calls, data processing, and error handling
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from functools import wraps
import requests
from cachetools import TTLCache
from tenacity import retry, stop_after_attempt, wait_exponential
import yaml
import os
from solana.rpc.api import Client
from solana.rpc.commitment import Commitment
from solders.pubkey import Pubkey
from solders.signature import Signature

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global cache for API responses
cache = TTLCache(maxsize=1000, ttl=300)  # 5-minute TTL

class ConfigManager:
    """Manages configuration loading and validation"""

    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = self.load_config()

    def load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r') as file:
                config = yaml.safe_load(file)

            # Load environment variables
            config['telegram']['bot_token'] = os.getenv('TELEGRAM_BOT_TOKEN', '')

            return config
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            raise

    def get(self, key_path: str, default=None):
        """Get nested configuration value using dot notation"""
        keys = key_path.split('.')
        value = self.config

        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default

        return value

class SolanaRPCClient:
    """Enhanced Solana RPC client with error handling and caching"""

    def __init__(self, config: ConfigManager):
        self.config = config
        self.endpoints = config.get('solana.rpc_endpoints', [])
        self.backup_endpoints = config.get('solana.backup_endpoints', [])
        self.current_endpoint_index = 0
        self.client = None
        self._initialize_client()

    def _initialize_client(self):
        """Initialize RPC client with current endpoint"""
        if self.endpoints:
            endpoint = self.endpoints[self.current_endpoint_index]
            self.client = Client(endpoint)
            logger.info(f"Initialized Solana RPC client with endpoint: {endpoint}")

    def _rotate_endpoint(self):
        """Rotate to next available endpoint"""
        self.current_endpoint_index = (self.current_endpoint_index + 1) % len(self.endpoints)
        self._initialize_client()
        logger.warning(f"Rotated to endpoint: {self.endpoints[self.current_endpoint_index]}")

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def get_recent_blockhash(self):
        """Get recent blockhash with retry logic"""
        try:
            response = self.client.get_latest_blockhash()
            return response.value.blockhash
        except Exception as e:
            logger.error(f"Failed to get recent blockhash: {e}")
            self._rotate_endpoint()
            raise

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def get_signatures_for_address(self, address: str, limit: int = 100) -> List[Dict]:
        """Get transaction signatures for an address"""
        try:
            pubkey = Pubkey.from_string(address)
            response = self.client.get_signatures_for_address(
                pubkey,
                limit=limit,
                commitment=Commitment("confirmed")
            )
            return response.value if response.value else []
        except Exception as e:
            logger.error(f"Failed to get signatures for {address}: {e}")
            self._rotate_endpoint()
            raise

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def get_transaction(self, signature: str) -> Optional[Dict]:
        """Get transaction details by signature"""
        try:
            sig = Signature.from_string(signature)
            response = self.client.get_transaction(
                sig,
                encoding="jsonParsed",
                commitment=Commitment("confirmed")
            )
            return response.value if response.value else None
        except Exception as e:
            logger.error(f"Failed to get transaction {signature}: {e}")
            return None

class RateLimiter:
    """Rate limiter to prevent API abuse"""

    def __init__(self, max_calls: int, time_window: int):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []

    def wait_if_needed(self):
        """Wait if rate limit would be exceeded"""
        now = time.time()

        # Remove old calls outside the time window
        self.calls = [call_time for call_time in self.calls if now - call_time < self.time_window]

        # Check if we need to wait
        if len(self.calls) >= self.max_calls:
            sleep_time = self.time_window - (now - self.calls[0])
            if sleep_time > 0:
                logger.info(f"Rate limit reached, sleeping for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)

        # Record this call
        self.calls.append(now)

def rate_limit(max_calls: int, time_window: int):
    """Decorator for rate limiting function calls"""
    limiter = RateLimiter(max_calls, time_window)

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            limiter.wait_if_needed()
            return func(*args, **kwargs)
        return wrapper
    return decorator

def cached_request(cache_key: str, ttl: int = 300):
    """Decorator for caching function results"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            key = f"{cache_key}:{hash(str(args) + str(kwargs))}"

            if key in cache:
                logger.debug(f"Cache hit for {key}")
                return cache[key]

            result = func(*args, **kwargs)
            cache[key] = result
            logger.debug(f"Cached result for {key}")
            return result
        return wrapper
    return decorator

class DataProcessor:
    """Utility class for processing token and transaction data"""

    @staticmethod
    def extract_token_info(transaction: Dict) -> Optional[Dict]:
        """Extract token information from a transaction"""
        try:
            if not transaction or 'meta' not in transaction:
                return None

            meta = transaction['meta']
            if meta.get('err'):
                return None

            # Look for token creation in the transaction
            instructions = transaction.get('transaction', {}).get('message', {}).get('instructions', [])

            for instruction in instructions:
                if instruction.get('programId') == 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA':
                    # This is a token program instruction
                    parsed = instruction.get('parsed', {})
                    if parsed.get('type') == 'initializeMint':
                        return {
                            'mint': parsed.get('info', {}).get('mint'),
                            'decimals': parsed.get('info', {}).get('decimals'),
                            'timestamp': transaction.get('blockTime'),
                            'signature': transaction.get('transaction', {}).get('signatures', [None])[0]
                        }

            return None
        except Exception as e:
            logger.error(f"Error extracting token info: {e}")
            return None

    @staticmethod
    def calculate_buy_rate(transactions: List[Dict], time_window_minutes: int = 1) -> float:
        """Calculate buy rate per minute from transaction list"""
        try:
            if not transactions:
                return 0.0

            current_time = time.time()
            cutoff_time = current_time - (time_window_minutes * 60)

            buy_count = 0
            for tx in transactions:
                if tx.get('blockTime', 0) >= cutoff_time:
                    # Simple heuristic: if transaction succeeded and has token transfers, count as buy
                    if not tx.get('meta', {}).get('err'):
                        buy_count += 1

            return buy_count / time_window_minutes
        except Exception as e:
            logger.error(f"Error calculating buy rate: {e}")
            return 0.0

    @staticmethod
    def estimate_market_cap(token_address: str, price_usd: float, total_supply: int) -> float:
        """Estimate market cap from price and supply"""
        try:
            return price_usd * total_supply
        except Exception as e:
            logger.error(f"Error calculating market cap: {e}")
            return 0.0

    @staticmethod
    def calculate_holder_ratio(token_address: str, total_holders: int, total_transactions: int) -> float:
        """Calculate unique holders ratio"""
        try:
            if total_transactions == 0:
                return 0.0
            return min(total_holders / total_transactions, 1.0)
        except Exception as e:
            logger.error(f"Error calculating holder ratio: {e}")
            return 0.0

class ErrorHandler:
    """Centralized error handling and logging"""

    def __init__(self, max_consecutive_errors: int = 10):
        self.max_consecutive_errors = max_consecutive_errors
        self.consecutive_errors = 0
        self.last_error_time = 0

    def handle_error(self, error: Exception, context: str = "") -> bool:
        """Handle an error and return whether to continue processing"""
        self.consecutive_errors += 1
        self.last_error_time = time.time()

        logger.error(f"Error in {context}: {error}")

        if self.consecutive_errors >= self.max_consecutive_errors:
            logger.critical(f"Too many consecutive errors ({self.consecutive_errors}). Stopping.")
            return False

        return True

    def reset_error_count(self):
        """Reset error count after successful operation"""
        if self.consecutive_errors > 0:
            logger.info(f"Resetting error count after {self.consecutive_errors} consecutive errors")
            self.consecutive_errors = 0

    def should_continue(self) -> bool:
        """Check if processing should continue"""
        return self.consecutive_errors < self.max_consecutive_errors

def format_number(num: float, decimals: int = 2) -> str:
    """Format number with appropriate suffixes (K, M, B)"""
    try:
        if num >= 1_000_000_000:
            return f"{num / 1_000_000_000:.{decimals}f}B"
        elif num >= 1_000_000:
            return f"{num / 1_000_000:.{decimals}f}M"
        elif num >= 1_000:
            return f"{num / 1_000:.{decimals}f}K"
        else:
            return f"{num:.{decimals}f}"
    except:
        return str(num)

def format_percentage(value: float, decimals: int = 1) -> str:
    """Format percentage with % symbol"""
    try:
        return f"{value:.{decimals}f}%"
    except:
        return "0.0%"

def save_json_data(data: Any, filepath: str) -> bool:
    """Safely save data to JSON file"""
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        return True
    except Exception as e:
        logger.error(f"Failed to save JSON data to {filepath}: {e}")
        return False

def load_json_data(filepath: str, default=None) -> Any:
    """Safely load data from JSON file"""
    try:
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                return json.load(f)
        return default or {}
    except Exception as e:
        logger.error(f"Failed to load JSON data from {filepath}: {e}")
        return default or {}