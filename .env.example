# Monaco v1.0 Environment Configuration
# Copy this file to .env and fill in your actual values

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# Optional: Override default settings
# TELEGRAM_CHANNEL_ID=@your_channel
# TELEGRAM_CHAT_ID=your_chat_id

# Solana RPC Configuration (optional - defaults are provided)
# SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# Logging Configuration (optional)
# LOG_LEVEL=INFO
# LOG_FILE=logs/monaco.log

# Instructions:
# 1. Get a Telegram bot token from @BotFather on Telegram
# 2. Add your bot to your target channel as an admin
# 3. Copy this file to .env and add your bot token
# 4. Run: python main.py
