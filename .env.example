# Monaco v1.0 Environment Configuration
# Copy this file to .env and fill in your actual values

# Telegram Bot Configuration (for posting alerts)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# Telethon Configuration (for GMGN group monitoring)
# Get these from https://my.telegram.org
TELEGRAM_API_ID=your_api_id_here
TELEGRAM_API_HASH=your_api_hash_here

# Optional: Session string (generated after first login)
# TELEGRAM_SESSION_STRING=your_session_string_here

# Optional: Override default settings
# TELEGRAM_CHANNEL_ID=@your_channel
# TELEGRAM_CHAT_ID=your_chat_id

# Solana RPC Configuration (optional - defaults are provided)
# SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# Logging Configuration (optional)
# LOG_LEVEL=INFO
# LOG_FILE=logs/monaco.log

# Setup Instructions:
# 1. Get Telegram bot token from @BotFather
# 2. Get Telegram API credentials from https://my.telegram.org
# 3. Add your bot to your target channel as an admin
# 4. Copy this file to .env and fill in your values
# 5. Run: python main.py
# 6. Follow prompts for first-time Telegram authentication
# 7. Configure operation mode in config.yaml (test/live)
