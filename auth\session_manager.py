"""
Monaco v1.0 - Telegram Session Manager
Secure Telegram user session management with Telethon for group monitoring
"""

import os
import logging
import asyncio
import json
from typing import Optional, Dict, Any, Callable
from datetime import datetime
from telethon import TelegramClient, events
from telethon.sessions import StringSession
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError
from telethon.tl.types import User, Chat, Channel
from utils.helpers import ConfigManager, save_json_data, load_json_data

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelegramSessionManager:
    """Manages Telegram user session with Telethon for group monitoring"""
    
    def __init__(self):
        self.config = ConfigManager()
        
        # Telegram API credentials (get from https://my.telegram.org)
        self.api_id = self.config.get('telethon.api_id') or os.getenv('TELEGRAM_API_ID')
        self.api_hash = self.config.get('telethon.api_hash') or os.getenv('TELEGRAM_API_HASH')
        self.session_string = self.config.get('telethon.session_string') or os.getenv('TELEGRAM_SESSION_STRING')
        
        # Session management
        self.client: Optional[TelegramClient] = None
        self.is_connected = False
        self.session_file = "auth/session_data.json"
        
        # Event handlers
        self.message_handlers: Dict[str, Callable] = {}
        
        # Connection state
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        
        logger.info("TelegramSessionManager initialized")
    
    async def initialize_session(self) -> bool:
        """Initialize Telegram session with authentication"""
        try:
            if not self.api_id or not self.api_hash:
                logger.error("Telegram API credentials not configured!")
                logger.error("Please set TELEGRAM_API_ID and TELEGRAM_API_HASH environment variables")
                logger.error("Get them from https://my.telegram.org")
                return False
            
            # Create client with session string if available
            if self.session_string:
                session = StringSession(self.session_string)
                logger.info("Using existing session string")
            else:
                session = StringSession()
                logger.info("Creating new session")
            
            self.client = TelegramClient(session, self.api_id, self.api_hash)
            
            # Connect to Telegram
            await self.client.connect()
            
            # Check if we're authorized
            if not await self.client.is_user_authorized():
                logger.info("User not authorized, starting authentication process...")
                success = await self._authenticate_user()
                if not success:
                    return False
            
            # Get user info
            me = await self.client.get_me()
            logger.info(f"Successfully connected as: {me.first_name} (@{me.username})")
            
            # Save session string for future use
            if not self.session_string:
                new_session_string = self.client.session.save()
                await self._save_session_data(new_session_string)
                logger.info("Session string saved for future use")
            
            self.is_connected = True
            self.reconnect_attempts = 0
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Telegram session: {e}")
            return False
    
    async def _authenticate_user(self) -> bool:
        """Handle user authentication process"""
        try:
            # Request phone number
            phone = input("Enter your phone number (with country code, e.g., +1234567890): ")
            
            # Send code request
            await self.client.send_code_request(phone)
            
            # Request verification code
            code = input("Enter the verification code you received: ")
            
            try:
                # Sign in with code
                await self.client.sign_in(phone, code)
                logger.info("Successfully authenticated with verification code")
                return True
                
            except SessionPasswordNeededError:
                # 2FA is enabled
                password = input("Enter your 2FA password: ")
                await self.client.sign_in(password=password)
                logger.info("Successfully authenticated with 2FA password")
                return True
                
        except PhoneCodeInvalidError:
            logger.error("Invalid verification code")
            return False
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return False
    
    async def _save_session_data(self, session_string: str):
        """Save session data securely"""
        try:
            session_data = {
                'session_string': session_string,
                'created_at': datetime.now().isoformat(),
                'api_id': self.api_id
            }
            
            save_json_data(session_data, self.session_file)
            logger.info("Session data saved successfully")
            
        except Exception as e:
            logger.error(f"Failed to save session data: {e}")
    
    async def add_message_handler(self, group_id: str, handler: Callable):
        """Add message handler for specific group"""
        try:
            if not self.client or not self.is_connected:
                logger.error("Client not connected")
                return False
            
            # Store handler
            self.message_handlers[group_id] = handler
            
            # Add event handler
            @self.client.on(events.NewMessage(chats=[group_id]))
            async def message_handler(event):
                try:
                    # Get message details
                    message_data = {
                        'id': event.message.id,
                        'text': event.message.text or '',
                        'date': event.message.date,
                        'sender_id': event.sender_id,
                        'chat_id': event.chat_id,
                        'raw_message': event.message
                    }
                    
                    # Call registered handler
                    if group_id in self.message_handlers:
                        await self.message_handlers[group_id](message_data)
                        
                except Exception as e:
                    logger.error(f"Error in message handler: {e}")
            
            logger.info(f"Message handler added for group: {group_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add message handler: {e}")
            return False
    
    async def get_group_info(self, group_identifier: str) -> Optional[Dict]:
        """Get information about a group/channel"""
        try:
            if not self.client or not self.is_connected:
                logger.error("Client not connected")
                return None
            
            entity = await self.client.get_entity(group_identifier)
            
            if isinstance(entity, (Chat, Channel)):
                return {
                    'id': entity.id,
                    'title': entity.title,
                    'username': getattr(entity, 'username', None),
                    'type': 'channel' if isinstance(entity, Channel) else 'chat',
                    'participants_count': getattr(entity, 'participants_count', 0)
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get group info for {group_identifier}: {e}")
            return None
    
    async def join_group(self, group_identifier: str) -> bool:
        """Join a group or channel"""
        try:
            if not self.client or not self.is_connected:
                logger.error("Client not connected")
                return False
            
            await self.client(JoinChannelRequest(group_identifier))
            logger.info(f"Successfully joined group: {group_identifier}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to join group {group_identifier}: {e}")
            return False
    
    async def start_monitoring(self):
        """Start monitoring for messages"""
        try:
            if not self.client or not self.is_connected:
                logger.error("Client not connected")
                return False
            
            logger.info("Starting Telegram message monitoring...")
            await self.client.run_until_disconnected()
            
        except Exception as e:
            logger.error(f"Error during monitoring: {e}")
            await self._handle_disconnection()
    
    async def _handle_disconnection(self):
        """Handle client disconnection and attempt reconnection"""
        try:
            self.is_connected = False
            
            if self.reconnect_attempts < self.max_reconnect_attempts:
                self.reconnect_attempts += 1
                logger.warning(f"Connection lost, attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")
                
                await asyncio.sleep(5 * self.reconnect_attempts)  # Exponential backoff
                
                success = await self.initialize_session()
                if success:
                    logger.info("Reconnection successful")
                    # Re-add message handlers
                    await self._restore_handlers()
                else:
                    logger.error("Reconnection failed")
            else:
                logger.error("Max reconnection attempts reached")
                
        except Exception as e:
            logger.error(f"Error during reconnection: {e}")
    
    async def _restore_handlers(self):
        """Restore message handlers after reconnection"""
        try:
            handlers_to_restore = list(self.message_handlers.items())
            self.message_handlers.clear()
            
            for group_id, handler in handlers_to_restore:
                await self.add_message_handler(group_id, handler)
                
        except Exception as e:
            logger.error(f"Error restoring handlers: {e}")
    
    async def disconnect(self):
        """Disconnect from Telegram"""
        try:
            if self.client and self.is_connected:
                await self.client.disconnect()
                self.is_connected = False
                logger.info("Disconnected from Telegram")
                
        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get current connection status"""
        return {
            'connected': self.is_connected,
            'reconnect_attempts': self.reconnect_attempts,
            'handlers_count': len(self.message_handlers),
            'client_initialized': self.client is not None
        }

# Global session manager instance
_session_manager = None

def get_session_manager() -> TelegramSessionManager:
    """Get or create global session manager instance"""
    global _session_manager
    if _session_manager is None:
        _session_manager = TelegramSessionManager()
    return _session_manager
