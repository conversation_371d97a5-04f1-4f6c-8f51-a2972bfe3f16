"""
Monaco v1.0 - Trigger Detector
Parse GMGN Signals messages for trigger events and extract Contract Addresses
"""

import re
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from utils.helpers import ConfigManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TriggerDetector:
    """Detects trigger events and extracts contract addresses from Telegram messages"""
    
    def __init__(self):
        self.config = ConfigManager()
        
        # Load trigger patterns from config
        self.trigger_patterns = self.config.get('trigger_detection.patterns', {
            'advertised': ['advertised', 'ad buy', 'advertisement'],
            'kol_buy': ['kol buy', 'kol bought', 'influencer buy'],
            'fdv_surge': ['fdv surge', 'fdv pump', 'market cap surge'],
            'smart_money': ['smart money', 'whale buy', 'smart money fomo'],
            'volume_spike': ['volume spike', 'volume surge', 'high volume'],
            'price_pump': ['price pump', 'pumping', 'mooning']
        })
        
        # Contract address patterns (Solana addresses are base58, 32-44 chars)
        self.ca_patterns = [
            r'\b[1-9A-HJ-NP-Za-km-z]{32,44}\b',  # Standard Solana address
            r'CA:\s*([1-9A-HJ-NP-Za-km-z]{32,44})',  # CA: prefix
            r'Contract:\s*([1-9A-HJ-NP-Za-km-z]{32,44})',  # Contract: prefix
            r'Address:\s*([1-9A-HJ-NP-Za-km-z]{32,44})',  # Address: prefix
            r'Token:\s*([1-9A-HJ-NP-Za-km-z]{32,44})',  # Token: prefix
        ]
        
        # Compile regex patterns for efficiency
        self.compiled_ca_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.ca_patterns]
        
        # Known false positive addresses to filter out
        self.blacklisted_addresses = {
            'So11111111111111111111111111111111111111112',  # Wrapped SOL
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',  # USDT
            '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',  # RAY
        }
        
        # Message processing stats
        self.stats = {
            'messages_processed': 0,
            'triggers_detected': 0,
            'cas_extracted': 0,
            'unique_cas': set()
        }
        
        logger.info("TriggerDetector initialized")
    
    def extract_contract_addresses(self, text: str) -> List[str]:
        """Extract contract addresses from message text"""
        try:
            addresses = []
            
            for pattern in self.compiled_ca_patterns:
                matches = pattern.findall(text)
                for match in matches:
                    # Handle tuple results from groups
                    address = match if isinstance(match, str) else match[0] if match else None
                    if address and self._is_valid_solana_address(address):
                        addresses.append(address)
            
            # Remove duplicates and blacklisted addresses
            unique_addresses = []
            for addr in addresses:
                if addr not in self.blacklisted_addresses and addr not in unique_addresses:
                    unique_addresses.append(addr)
            
            return unique_addresses
            
        except Exception as e:
            logger.error(f"Error extracting contract addresses: {e}")
            return []
    
    def _is_valid_solana_address(self, address: str) -> bool:
        """Validate if string is a valid Solana address"""
        try:
            # Basic validation
            if not address or len(address) < 32 or len(address) > 44:
                return False
            
            # Check if it contains only valid base58 characters
            valid_chars = set('123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz')
            if not all(c in valid_chars for c in address):
                return False
            
            # Additional checks can be added here
            return True
            
        except Exception:
            return False
    
    def detect_triggers(self, text: str) -> List[Dict[str, str]]:
        """Detect trigger events in message text"""
        try:
            detected_triggers = []
            text_lower = text.lower()
            
            for trigger_type, patterns in self.trigger_patterns.items():
                for pattern in patterns:
                    if pattern.lower() in text_lower:
                        detected_triggers.append({
                            'type': trigger_type,
                            'pattern': pattern,
                            'confidence': self._calculate_confidence(text_lower, pattern)
                        })
                        break  # Only count each trigger type once per message
            
            return detected_triggers
            
        except Exception as e:
            logger.error(f"Error detecting triggers: {e}")
            return []
    
    def _calculate_confidence(self, text: str, pattern: str) -> float:
        """Calculate confidence score for trigger detection"""
        try:
            # Base confidence
            confidence = 0.7
            
            # Boost confidence for exact matches
            if pattern in text:
                confidence += 0.2
            
            # Boost for context words
            context_words = ['buy', 'pump', 'surge', 'spike', 'fomo', 'whale']
            for word in context_words:
                if word in text:
                    confidence += 0.05
            
            # Cap at 1.0
            return min(confidence, 1.0)
            
        except Exception:
            return 0.5
    
    def process_message(self, message_data: Dict) -> Optional[Dict]:
        """Process a Telegram message and extract relevant information"""
        try:
            self.stats['messages_processed'] += 1
            
            text = message_data.get('text', '')
            if not text:
                return None
            
            # Extract contract addresses
            contract_addresses = self.extract_contract_addresses(text)
            
            # Detect triggers
            triggers = self.detect_triggers(text)
            
            # Skip if no triggers or CAs found
            if not triggers and not contract_addresses:
                return None
            
            # Update stats
            if triggers:
                self.stats['triggers_detected'] += 1
            
            if contract_addresses:
                self.stats['cas_extracted'] += len(contract_addresses)
                self.stats['unique_cas'].update(contract_addresses)
            
            # Create result
            result = {
                'message_id': message_data.get('id'),
                'timestamp': message_data.get('date', datetime.now()).isoformat(),
                'sender_id': message_data.get('sender_id'),
                'chat_id': message_data.get('chat_id'),
                'original_text': text,
                'contract_addresses': contract_addresses,
                'triggers': triggers,
                'trigger_count': len(triggers),
                'ca_count': len(contract_addresses),
                'processed_at': datetime.now().isoformat()
            }
            
            # Calculate overall signal strength
            result['signal_strength'] = self._calculate_signal_strength(triggers, contract_addresses)
            
            logger.info(f"Processed message: {len(triggers)} triggers, {len(contract_addresses)} CAs")
            return result
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return None
    
    def _calculate_signal_strength(self, triggers: List[Dict], contract_addresses: List[str]) -> float:
        """Calculate overall signal strength score"""
        try:
            score = 0.0
            
            # Base score for having triggers
            if triggers:
                avg_confidence = sum(t.get('confidence', 0.5) for t in triggers) / len(triggers)
                score += avg_confidence * 0.6
            
            # Score for contract addresses
            if contract_addresses:
                score += min(len(contract_addresses) * 0.2, 0.4)
            
            # Bonus for multiple triggers
            if len(triggers) > 1:
                score += 0.1
            
            # Bonus for high-value trigger types
            high_value_triggers = ['smart_money', 'kol_buy', 'fdv_surge']
            for trigger in triggers:
                if trigger.get('type') in high_value_triggers:
                    score += 0.1
                    break
            
            return min(score, 1.0)
            
        except Exception:
            return 0.5
    
    def get_stats(self) -> Dict:
        """Get processing statistics"""
        stats = self.stats.copy()
        stats['unique_cas_count'] = len(stats['unique_cas'])
        stats['unique_cas'] = list(stats['unique_cas'])
        return stats
    
    def reset_stats(self):
        """Reset processing statistics"""
        self.stats = {
            'messages_processed': 0,
            'triggers_detected': 0,
            'cas_extracted': 0,
            'unique_cas': set()
        }
        logger.info("Statistics reset")
    
    def add_custom_trigger(self, trigger_type: str, patterns: List[str]):
        """Add custom trigger patterns"""
        try:
            if trigger_type not in self.trigger_patterns:
                self.trigger_patterns[trigger_type] = []
            
            self.trigger_patterns[trigger_type].extend(patterns)
            logger.info(f"Added {len(patterns)} patterns for trigger type: {trigger_type}")
            
        except Exception as e:
            logger.error(f"Error adding custom trigger: {e}")
    
    def blacklist_address(self, address: str):
        """Add address to blacklist"""
        try:
            if self._is_valid_solana_address(address):
                self.blacklisted_addresses.add(address)
                logger.info(f"Added address to blacklist: {address}")
            else:
                logger.warning(f"Invalid address for blacklist: {address}")
                
        except Exception as e:
            logger.error(f"Error blacklisting address: {e}")

# Global detector instance
_detector_instance = None

def get_trigger_detector() -> TriggerDetector:
    """Get or create global trigger detector instance"""
    global _detector_instance
    if _detector_instance is None:
        _detector_instance = TriggerDetector()
    return _detector_instance

def process_telegram_message(message_data: Dict) -> Optional[Dict]:
    """Process a Telegram message for triggers and CAs"""
    detector = get_trigger_detector()
    return detector.process_message(message_data)
