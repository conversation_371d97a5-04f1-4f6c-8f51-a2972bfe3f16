"""
Monaco v1.0 - Main Entry Point
Cryptocurrency token detection and alert system for Pump.fun and Solana
"""

import os
import sys
import logging
import signal
import time
from datetime import datetime
from utils.helpers import ConfigManager
from launch_watcher import watch_loop
from pnl_tracker import start_pnl_tracking
from autoposter import send_status_message

# Configure logging
def setup_logging():
    """Setup comprehensive logging configuration"""
    config = ConfigManager()

    # Create logs directory
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)

    # Configure logging
    log_level = config.get('logging.level', 'INFO')
    log_file = config.get('logging.file', 'logs/monaco.log')

    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )

    logger = logging.getLogger(__name__)
    logger.info(f"Monaco v1.0 logging initialized - Level: {log_level}")
    return logger

def validate_configuration():
    """Validate critical configuration settings"""
    config = ConfigManager()
    logger = logging.getLogger(__name__)

    issues = []
    warnings = []

    # Check operation mode
    operation_mode = config.get('operation_mode.current_mode', 'test')
    if operation_mode not in ['test', 'live']:
        issues.append(f"Invalid operation mode: {operation_mode}. Must be 'test' or 'live'")

    # Check Telegram bot configuration
    bot_token = config.get('telegram.bot_token')
    if not bot_token:
        issues.append("Telegram bot token not configured (set TELEGRAM_BOT_TOKEN environment variable)")

    channel_id = config.get('telegram.channel_id')
    if not channel_id:
        issues.append("Telegram channel ID not configured")

    # Check Telethon configuration for group monitoring
    api_id = config.get('telethon.api_id') or os.getenv('TELEGRAM_API_ID')
    api_hash = config.get('telethon.api_hash') or os.getenv('TELEGRAM_API_HASH')

    if not api_id or not api_hash:
        warnings.append("Telethon API credentials not configured - group monitoring will be disabled")
        warnings.append("Get API credentials from https://my.telegram.org")

    # Check GMGN monitoring settings
    gmgn_enabled = config.get('gmgn_monitoring.enabled', True)
    if gmgn_enabled and (not api_id or not api_hash):
        warnings.append("GMGN monitoring enabled but Telethon credentials missing")

    # Check Solana RPC endpoints
    rpc_endpoints = config.get('solana.rpc_endpoints', [])
    if not rpc_endpoints:
        issues.append("No Solana RPC endpoints configured")

    # Check data directories
    data_dir = "data"
    if not os.path.exists(data_dir):
        try:
            os.makedirs(data_dir, exist_ok=True)
            logger.info(f"Created data directory: {data_dir}")
        except Exception as e:
            issues.append(f"Cannot create data directory: {e}")

    # Create auth directory
    auth_dir = "auth"
    if not os.path.exists(auth_dir):
        try:
            os.makedirs(auth_dir, exist_ok=True)
            logger.info(f"Created auth directory: {auth_dir}")
        except Exception as e:
            issues.append(f"Cannot create auth directory: {e}")

    # Report warnings
    if warnings:
        logger.warning("Configuration warnings:")
        for warning in warnings:
            logger.warning(f"  - {warning}")

    # Report issues
    if issues:
        logger.error("Configuration validation failed:")
        for issue in issues:
            logger.error(f"  - {issue}")
        return False

    logger.info("Configuration validation passed")
    return True

def print_startup_banner():
    """Print startup banner with system information"""
    config = ConfigManager()
    logger = logging.getLogger(__name__)

    operation_mode = config.get('operation_mode.current_mode', 'test').upper()

    banner = f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                        Monaco v1.0                          ║
    ║           Advanced Cryptocurrency Detection Engine           ║
    ║                                                              ║
    ║  🔗 Solana Blockchain Monitoring                            ║
    ║  📱 Telegram Signal Integration (GMGN)                      ║
    ║  📊 Advanced Token Analysis & Risk Assessment               ║
    ║  📈 Real-time PnL Tracking                                  ║
    ║  🎯 Dual-Source Signal Validation                           ║
    ║                                                              ║
    ║  Mode: {operation_mode:<8}                                      ║
    ╚══════════════════════════════════════════════════════════════╝
    """

    print(banner)
    logger.info(f"Monaco v1.0 - Advanced Cryptocurrency Detection Engine ({operation_mode} MODE)")
    logger.info(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")

    # Log configuration summary
    logger.info("Configuration Summary:")
    logger.info(f"  Operation Mode: {operation_mode}")
    logger.info(f"  Target Channel: {config.get('telegram.channel_id', 'Not configured')}")
    logger.info(f"  GMGN Monitoring: {'ENABLED' if config.get('gmgn_monitoring.enabled', True) else 'DISABLED'}")
    logger.info(f"  GMGN Group: {config.get('gmgn_monitoring.group_identifier', 'gmgn_signals')}")
    logger.info(f"  GMGN Min Signal: {config.get('gmgn_monitoring.min_signal_strength', 0.5)}")
    logger.info(f"  Poll Interval: {config.get('monitoring.poll_interval_seconds', 10)}s")
    logger.info(f"  Max Market Cap: ${config.get('token_analysis.max_initial_mc', 15000):,}")
    logger.info(f"  Min Buy Rate: {config.get('token_analysis.min_buy_rate_per_min', 2)}/min")
    logger.info(f"  RPC Endpoints: {len(config.get('solana.rpc_endpoints', []))}")
    logger.info(f"  Signal Validation: {'ENABLED' if config.get('signal_validation.enabled', True) else 'DISABLED'}")
    logger.info(f"  Chart Previews: {'ENABLED' if config.get('dex_preview.enabled', True) else 'DISABLED'}")
    logger.info(f"  Cross-Reference: {'ENABLED' if config.get('signal_validation.cross_reference_blockchain', True) else 'DISABLED'}")

    # Mode-specific information
    if operation_mode == 'TEST':
        logger.info("TEST MODE: Monitoring and analysis active, alerts disabled")
        test_duration = config.get('operation_mode.test_mode.test_duration_hours', 24)
        logger.info(f"Test duration: {test_duration} hours")
    else:
        logger.info("LIVE MODE: Full functionality with real-time alerts")
        max_alerts = config.get('operation_mode.live_mode.max_alerts_per_hour', 10)
        logger.info(f"Max alerts per hour: {max_alerts}")

def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown"""
    logger = logging.getLogger(__name__)

    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")

        # Send shutdown notification
        try:
            send_status_message("🛑 Monaco Bot shutting down...")
        except:
            pass

        # Exit gracefully
        sys.exit(0)

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    logger.info("Signal handlers registered for graceful shutdown")

def check_dependencies():
    """Check if all required dependencies are available"""
    logger = logging.getLogger(__name__)

    required_modules = [
        'solana', 'telegram', 'telethon', 'requests', 'yaml',
        'cachetools', 'tenacity', 'pandas', 'matplotlib', 'PIL'
    ]

    missing_modules = []
    optional_missing = []

    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            if module in ['matplotlib', 'PIL']:
                optional_missing.append(module)
            else:
                missing_modules.append(module)

    if missing_modules:
        logger.error("Missing required dependencies:")
        for module in missing_modules:
            logger.error(f"  - {module}")
        logger.error("Please install missing dependencies: pip install -r requirements.txt")
        return False

    if optional_missing:
        logger.warning("Missing optional dependencies (chart generation may be disabled):")
        for module in optional_missing:
            logger.warning(f"  - {module}")

    logger.info("All required dependencies are available")
    return True

def initialize_system():
    """Initialize all system components"""
    logger = logging.getLogger(__name__)

    try:
        # Start PnL tracking in background
        logger.info("Starting PnL tracking system...")
        start_pnl_tracking()

        # Send startup notification
        try:
            send_status_message("🚀 Monaco Bot v1.0 started successfully!")
        except Exception as e:
            logger.warning(f"Failed to send startup notification: {e}")

        logger.info("System initialization completed successfully")
        return True

    except Exception as e:
        logger.error(f"System initialization failed: {e}")
        return False

def main():
    """Main entry point with comprehensive error handling"""

    # Setup logging first
    logger = setup_logging()

    try:
        # Print startup banner
        print_startup_banner()

        # Setup signal handlers
        setup_signal_handlers()

        # Check dependencies
        if not check_dependencies():
            logger.error("Dependency check failed, exiting...")
            sys.exit(1)

        # Validate configuration
        if not validate_configuration():
            logger.error("Configuration validation failed, exiting...")
            sys.exit(1)

        # Initialize system components
        if not initialize_system():
            logger.error("System initialization failed, exiting...")
            sys.exit(1)

        # Start main monitoring loop
        logger.info("Starting main token monitoring loop...")
        watch_loop()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.critical(f"Critical error in main: {e}")
        try:
            send_status_message(f"🚨 Critical error: {str(e)[:100]}...")
        except:
            pass
        sys.exit(1)
    finally:
        logger.info("Monaco v1.0 shutdown complete")

if __name__ == '__main__':
    main()