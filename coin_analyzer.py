"""
Monaco v1.0 - Coin Analyzer
Advanced token analysis engine for evaluating cryptocurrency potential
"""

import logging
import time
import requests
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from utils.helpers import (
    ConfigManager, SolanaRPCClient, DataProcessor,
    rate_limit, cached_request, format_number, format_percentage
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TokenAnalyzer:
    """Comprehensive token analysis engine"""

    def __init__(self):
        self.config = ConfigManager()
        self.solana_client = SolanaRPCClient(self.config)
        self.data_processor = DataProcessor()

        # Analysis criteria from config
        self.min_buy_rate = self.config.get('token_analysis.min_buy_rate_per_min', 2)
        self.max_initial_mc = self.config.get('token_analysis.max_initial_mc', 15000)
        self.min_holders_ratio = self.config.get('token_analysis.min_holders_ratio', 0.5)
        self.min_liquidity = self.config.get('token_analysis.min_liquidity_usd', 1000)
        self.max_age_minutes = self.config.get('token_analysis.max_age_minutes', 60)
        self.min_volume_24h = self.config.get('token_analysis.min_volume_24h', 5000)

        logger.info("TokenAnalyzer initialized with criteria:")
        logger.info(f"  Min buy rate: {self.min_buy_rate}/min")
        logger.info(f"  Max initial MC: ${format_number(self.max_initial_mc)}")
        logger.info(f"  Min holders ratio: {format_percentage(self.min_holders_ratio * 100)}")

    @cached_request("token_price", ttl=60)  # Cache for 1 minute
    def get_token_price_data(self, token_address: str) -> Optional[Dict]:
        """Get token price and market data from DexScreener API"""
        try:
            url = f"https://api.dexscreener.com/latest/dex/tokens/{token_address}"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                pairs = data.get('pairs', [])

                if pairs:
                    # Get the most liquid pair
                    best_pair = max(pairs, key=lambda p: float(p.get('liquidity', {}).get('usd', 0)))
                    return {
                        'price_usd': float(best_pair.get('priceUsd', 0)),
                        'liquidity_usd': float(best_pair.get('liquidity', {}).get('usd', 0)),
                        'volume_24h': float(best_pair.get('volume', {}).get('h24', 0)),
                        'price_change_24h': float(best_pair.get('priceChange', {}).get('h24', 0)),
                        'market_cap': float(best_pair.get('marketCap', 0)),
                        'dex': best_pair.get('dexId', 'unknown'),
                        'pair_address': best_pair.get('pairAddress', ''),
                        'created_at': best_pair.get('pairCreatedAt', 0)
                    }

            return None

        except Exception as e:
            logger.error(f"Failed to get price data for {token_address}: {e}")
            return None

    def get_token_transactions(self, token_address: str, limit: int = 100) -> List[Dict]:
        """Get recent transactions for a token"""
        try:
            signatures = self.solana_client.get_signatures_for_address(token_address, limit=limit)
            transactions = []

            for sig_info in signatures[:20]:  # Limit to recent 20 for performance
                tx = self.solana_client.get_transaction(sig_info['signature'])
                if tx:
                    transactions.append(tx)

            return transactions

        except Exception as e:
            logger.error(f"Failed to get transactions for {token_address}: {e}")
            return []

    def analyze_trading_activity(self, token_address: str) -> Dict:
        """Analyze trading activity and patterns"""
        try:
            transactions = self.get_token_transactions(token_address)

            if not transactions:
                return {
                    'buy_rate_per_min': 0,
                    'total_transactions': 0,
                    'unique_traders': 0,
                    'avg_transaction_size': 0,
                    'trading_score': 0
                }

            # Calculate buy rate
            buy_rate = self.data_processor.calculate_buy_rate(transactions, time_window_minutes=1)

            # Count unique traders (simplified)
            unique_addresses = set()
            total_volume = 0
            successful_txs = 0

            for tx in transactions:
                if not tx.get('meta', {}).get('err'):
                    successful_txs += 1
                    # Extract addresses from transaction
                    accounts = tx.get('transaction', {}).get('message', {}).get('accountKeys', [])
                    for account in accounts[:5]:  # Limit to avoid spam
                        if isinstance(account, str):
                            unique_addresses.add(account)

            unique_traders = len(unique_addresses)
            avg_tx_size = total_volume / max(successful_txs, 1)

            # Calculate trading score (0-100)
            trading_score = min(100, (
                (buy_rate / max(self.min_buy_rate, 1)) * 30 +
                (unique_traders / max(successful_txs, 1)) * 40 +
                min(successful_txs / 50, 1) * 30
            ))

            return {
                'buy_rate_per_min': buy_rate,
                'total_transactions': len(transactions),
                'successful_transactions': successful_txs,
                'unique_traders': unique_traders,
                'avg_transaction_size': avg_tx_size,
                'trading_score': trading_score
            }

        except Exception as e:
            logger.error(f"Failed to analyze trading activity for {token_address}: {e}")
            return {'trading_score': 0}

    def calculate_risk_score(self, token_data: Dict, price_data: Dict, trading_data: Dict) -> Dict:
        """Calculate comprehensive risk score"""
        try:
            risk_factors = []
            risk_score = 0  # 0 = low risk, 100 = high risk

            # Age factor
            token_age_minutes = self._get_token_age_minutes(token_data)
            if token_age_minutes > self.max_age_minutes:
                risk_factors.append(f"Token too old ({token_age_minutes:.1f} min)")
                risk_score += 20

            # Market cap factor
            market_cap = price_data.get('market_cap', 0)
            if market_cap > self.max_initial_mc:
                risk_factors.append(f"Market cap too high (${format_number(market_cap)})")
                risk_score += 25

            # Liquidity factor
            liquidity = price_data.get('liquidity_usd', 0)
            if liquidity < self.min_liquidity:
                risk_factors.append(f"Low liquidity (${format_number(liquidity)})")
                risk_score += 30

            # Trading activity factor
            buy_rate = trading_data.get('buy_rate_per_min', 0)
            if buy_rate < self.min_buy_rate:
                risk_factors.append(f"Low buy rate ({buy_rate:.1f}/min)")
                risk_score += 15

            # Volume factor
            volume_24h = price_data.get('volume_24h', 0)
            if volume_24h < self.min_volume_24h:
                risk_factors.append(f"Low 24h volume (${format_number(volume_24h)})")
                risk_score += 10

            return {
                'risk_score': min(risk_score, 100),
                'risk_level': self._get_risk_level(risk_score),
                'risk_factors': risk_factors
            }

        except Exception as e:
            logger.error(f"Failed to calculate risk score: {e}")
            return {'risk_score': 100, 'risk_level': 'HIGH', 'risk_factors': ['Analysis failed']}

    def _get_token_age_minutes(self, token_data: Dict) -> float:
        """Calculate token age in minutes"""
        try:
            timestamp = token_data.get('timestamp') or token_data.get('created_at', 0)
            if timestamp:
                if isinstance(timestamp, str):
                    # Parse ISO format
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    timestamp = dt.timestamp()

                age_seconds = time.time() - timestamp
                return age_seconds / 60

            return 0
        except:
            return 0

    def _get_risk_level(self, risk_score: float) -> str:
        """Convert risk score to risk level"""
        if risk_score <= 20:
            return 'LOW'
        elif risk_score <= 50:
            return 'MEDIUM'
        elif risk_score <= 80:
            return 'HIGH'
        else:
            return 'VERY_HIGH'

    def generate_recommendation(self, analysis_data: Dict) -> Dict:
        """Generate investment recommendation based on analysis"""
        try:
            risk_score = analysis_data.get('risk_analysis', {}).get('risk_score', 100)
            trading_score = analysis_data.get('trading_analysis', {}).get('trading_score', 0)

            # Calculate overall score
            overall_score = max(0, 100 - risk_score + trading_score) / 2

            # Generate recommendation
            if overall_score >= 80:
                recommendation = 'STRONG_BUY'
                confidence = 'HIGH'
            elif overall_score >= 60:
                recommendation = 'BUY'
                confidence = 'MEDIUM'
            elif overall_score >= 40:
                recommendation = 'HOLD'
                confidence = 'LOW'
            else:
                recommendation = 'AVOID'
                confidence = 'HIGH'

            return {
                'recommendation': recommendation,
                'confidence': confidence,
                'overall_score': overall_score,
                'reasoning': self._generate_reasoning(analysis_data)
            }

        except Exception as e:
            logger.error(f"Failed to generate recommendation: {e}")
            return {
                'recommendation': 'AVOID',
                'confidence': 'HIGH',
                'overall_score': 0,
                'reasoning': 'Analysis failed'
            }

    def _generate_reasoning(self, analysis_data: Dict) -> str:
        """Generate human-readable reasoning for recommendation"""
        try:
            reasons = []

            price_data = analysis_data.get('price_data', {})
            trading_data = analysis_data.get('trading_analysis', {})
            risk_data = analysis_data.get('risk_analysis', {})

            # Positive factors
            if trading_data.get('buy_rate_per_min', 0) >= self.min_buy_rate:
                reasons.append(f"Strong buying activity ({trading_data['buy_rate_per_min']:.1f} buys/min)")

            if price_data.get('liquidity_usd', 0) >= self.min_liquidity:
                reasons.append(f"Good liquidity (${format_number(price_data['liquidity_usd'])})")

            if price_data.get('volume_24h', 0) >= self.min_volume_24h:
                reasons.append(f"Healthy volume (${format_number(price_data['volume_24h'])})")

            # Risk factors
            risk_factors = risk_data.get('risk_factors', [])
            if risk_factors:
                reasons.extend([f"⚠️ {factor}" for factor in risk_factors[:3]])

            return "; ".join(reasons) if reasons else "Insufficient data for analysis"

        except Exception as e:
            logger.error(f"Failed to generate reasoning: {e}")
            return "Analysis error"

def analyze_token(token_data: Dict) -> Dict:
    """Main token analysis function - called by launch_watcher"""
    analyzer = TokenAnalyzer()

    try:
        token_address = token_data.get('mint') or token_data.get('address')
        if not token_address:
            logger.warning("Token data missing address")
            return {'is_promising': False, 'error': 'Missing token address'}

        logger.info(f"Analyzing token: {token_address}")

        # Get price and market data
        price_data = analyzer.get_token_price_data(token_address)
        if not price_data:
            logger.warning(f"No price data available for {token_address}")
            return {'is_promising': False, 'error': 'No price data available'}

        # Analyze trading activity
        trading_analysis = analyzer.analyze_trading_activity(token_address)

        # Calculate risk score
        risk_analysis = analyzer.calculate_risk_score(token_data, price_data, trading_analysis)

        # Compile analysis data
        analysis_data = {
            'token_data': token_data,
            'price_data': price_data,
            'trading_analysis': trading_analysis,
            'risk_analysis': risk_analysis
        }

        # Generate recommendation
        recommendation = analyzer.generate_recommendation(analysis_data)

        # Determine if token is promising
        is_promising = (
            recommendation['recommendation'] in ['STRONG_BUY', 'BUY'] and
            risk_analysis['risk_score'] <= 60 and
            trading_analysis['trading_score'] >= 30
        )

        result = {
            'is_promising': is_promising,
            'analysis_timestamp': datetime.now().isoformat(),
            'token_address': token_address,
            **analysis_data,
            'recommendation': recommendation
        }

        logger.info(f"Analysis complete for {token_address}: {'PROMISING' if is_promising else 'NOT PROMISING'}")
        return result

    except Exception as e:
        logger.error(f"Token analysis failed: {e}")
        return {
            'is_promising': False,
            'error': str(e),
            'analysis_timestamp': datetime.now().isoformat()
        }