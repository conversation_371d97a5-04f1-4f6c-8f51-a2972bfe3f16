# Core dependencies for Monaco v1.0 cryptocurrency detection system

# Solana blockchain interaction
solana==0.30.2
solders==0.18.1

# HTTP requests and web scraping
requests==2.31.0
aiohttp==3.9.1
beautifulsoup4==4.12.2

# Telegram integrations
python-telegram-bot==20.7  # For bot functionality
telethon==1.34.0           # For user client and group monitoring

# Configuration and data handling
PyYAML==6.0.1
python-dotenv==1.0.0

# Data processing and analysis
pandas==2.1.4
numpy==1.24.3

# Async programming
asyncio-throttle==1.0.2

# Logging and monitoring
structlog==23.2.0

# Rate limiting and caching
cachetools==5.3.2
ratelimit==2.2.1

# JSON handling and validation
jsonschema==4.20.0

# Time and date utilities
python-dateutil==2.8.2

# Error handling and retries
tenacity==8.2.3

# Environment and system utilities
psutil==5.9.6

# Image processing and chart generation
Pillow==10.1.0
matplotlib==3.8.2
seaborn==0.13.0

# Text processing and pattern matching
regex==2023.10.3

# Cryptographic utilities for session management
cryptography==41.0.8
