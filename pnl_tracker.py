"""
Monaco v1.0 - PnL Tracker
Tracks profit and loss performance of detected tokens over time
"""

import logging
import time
import threading
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import requests
from utils.helpers import (
    ConfigManager, save_json_data, load_json_data,
    format_number, format_percentage
)
from autoposter import send_status_message

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PnLTracker:
    """Tracks token performance and calculates profit/loss metrics"""

    def __init__(self):
        self.config = ConfigManager()

        # Configuration
        self.track_duration_hours = self.config.get('pnl_tracking.track_duration_hours', 24)
        self.check_interval_minutes = self.config.get('pnl_tracking.check_interval_minutes', 30)
        self.min_price_change_percent = self.config.get('pnl_tracking.min_price_change_percent', 5)

        # Data storage
        self.tracking_file = "data/pnl_tracking.json"
        self.performance_file = "data/performance_summary.json"

        # Load existing tracking data
        self.tracked_tokens = load_json_data(self.tracking_file, {})
        self.performance_history = load_json_data(self.performance_file, {})

        # Tracking state
        self.is_running = False
        self.tracking_thread = None

        logger.info(f"PnLTracker initialized - tracking {len(self.tracked_tokens)} tokens")

    def add_token_for_tracking(self, token_address: str, initial_data: Dict):
        """Add a new token to tracking list"""
        try:
            if token_address in self.tracked_tokens:
                logger.debug(f"Token {token_address} already being tracked")
                return

            # Extract initial price data
            price_data = initial_data.get('price_data', {})
            initial_price = price_data.get('price_usd', 0)

            if initial_price <= 0:
                logger.warning(f"Cannot track token {token_address}: no valid initial price")
                return

            # Create tracking entry
            tracking_entry = {
                'token_address': token_address,
                'start_time': datetime.now().isoformat(),
                'initial_price': initial_price,
                'initial_market_cap': price_data.get('market_cap', 0),
                'initial_liquidity': price_data.get('liquidity_usd', 0),
                'initial_volume_24h': price_data.get('volume_24h', 0),
                'recommendation': initial_data.get('recommendation', {}).get('recommendation', 'UNKNOWN'),
                'confidence': initial_data.get('recommendation', {}).get('confidence', 'LOW'),
                'price_history': [
                    {
                        'timestamp': datetime.now().isoformat(),
                        'price': initial_price,
                        'market_cap': price_data.get('market_cap', 0),
                        'volume_24h': price_data.get('volume_24h', 0)
                    }
                ],
                'status': 'tracking'
            }

            self.tracked_tokens[token_address] = tracking_entry
            self.save_tracking_data()

            logger.info(f"Added token {token_address} to tracking (initial price: ${initial_price:.8f})")

        except Exception as e:
            logger.error(f"Failed to add token for tracking: {e}")

    def get_current_price_data(self, token_address: str) -> Optional[Dict]:
        """Get current price data for a token"""
        try:
            url = f"https://api.dexscreener.com/latest/dex/tokens/{token_address}"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                pairs = data.get('pairs', [])

                if pairs:
                    # Get the most liquid pair
                    best_pair = max(pairs, key=lambda p: float(p.get('liquidity', {}).get('usd', 0)))
                    return {
                        'price_usd': float(best_pair.get('priceUsd', 0)),
                        'market_cap': float(best_pair.get('marketCap', 0)),
                        'volume_24h': float(best_pair.get('volume', {}).get('h24', 0)),
                        'price_change_24h': float(best_pair.get('priceChange', {}).get('h24', 0)),
                        'liquidity_usd': float(best_pair.get('liquidity', {}).get('usd', 0))
                    }

            return None

        except Exception as e:
            logger.error(f"Failed to get current price for {token_address}: {e}")
            return None

    def update_token_performance(self, token_address: str) -> Optional[Dict]:
        """Update performance data for a specific token"""
        try:
            if token_address not in self.tracked_tokens:
                return None

            tracking_data = self.tracked_tokens[token_address]

            # Check if tracking period has expired
            start_time = datetime.fromisoformat(tracking_data['start_time'])
            if datetime.now() - start_time > timedelta(hours=self.track_duration_hours):
                tracking_data['status'] = 'completed'
                logger.info(f"Tracking completed for {token_address}")

            # Get current price
            current_price_data = self.get_current_price_data(token_address)
            if not current_price_data:
                return None

            current_price = current_price_data['price_usd']
            initial_price = tracking_data['initial_price']

            # Calculate performance metrics
            price_change_percent = ((current_price - initial_price) / initial_price) * 100
            price_change_multiplier = current_price / initial_price

            # Update price history
            price_history = tracking_data.get('price_history', [])
            price_history.append({
                'timestamp': datetime.now().isoformat(),
                'price': current_price,
                'market_cap': current_price_data.get('market_cap', 0),
                'volume_24h': current_price_data.get('volume_24h', 0)
            })

            # Keep only recent history (last 100 entries)
            if len(price_history) > 100:
                price_history = price_history[-100:]

            tracking_data['price_history'] = price_history

            # Calculate additional metrics
            performance_metrics = {
                'current_price': current_price,
                'price_change_percent': price_change_percent,
                'price_change_multiplier': price_change_multiplier,
                'max_price': max([p['price'] for p in price_history]),
                'min_price': min([p['price'] for p in price_history]),
                'last_updated': datetime.now().isoformat()
            }

            # Calculate max gain/loss
            max_price = performance_metrics['max_price']
            min_price = performance_metrics['min_price']

            max_gain_percent = ((max_price - initial_price) / initial_price) * 100
            max_loss_percent = ((min_price - initial_price) / initial_price) * 100

            performance_metrics.update({
                'max_gain_percent': max_gain_percent,
                'max_loss_percent': max_loss_percent,
                'volatility': ((max_price - min_price) / initial_price) * 100
            })

            tracking_data['performance'] = performance_metrics

            # Check for significant price changes
            if abs(price_change_percent) >= self.min_price_change_percent:
                self.log_significant_change(token_address, tracking_data, performance_metrics)

            return performance_metrics

        except Exception as e:
            logger.error(f"Failed to update performance for {token_address}: {e}")
            return None

    def log_significant_change(self, token_address: str, tracking_data: Dict, performance: Dict):
        """Log and potentially alert on significant price changes"""
        try:
            price_change = performance['price_change_percent']
            current_price = performance['current_price']
            initial_price = tracking_data['initial_price']

            # Create log entry
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'token_address': token_address,
                'price_change_percent': price_change,
                'current_price': current_price,
                'initial_price': initial_price,
                'recommendation': tracking_data.get('recommendation', 'UNKNOWN')
            }

            # Add to performance history
            date_key = datetime.now().strftime('%Y-%m-%d')
            if date_key not in self.performance_history:
                self.performance_history[date_key] = []

            self.performance_history[date_key].append(log_entry)

            # Log the change
            direction = "📈" if price_change > 0 else "📉"
            logger.info(f"{direction} {token_address}: {price_change:+.1f}% (${initial_price:.8f} → ${current_price:.8f})")

            # Send Telegram notification for significant changes
            if abs(price_change) >= 50:  # 50% change threshold
                message = f"{direction} Significant price change detected!\n"
                message += f"Token: {token_address}\n"
                message += f"Change: {price_change:+.1f}%\n"
                message += f"Price: ${initial_price:.8f} → ${current_price:.8f}\n"
                message += f"Multiplier: {performance['price_change_multiplier']:.2f}x"

                send_status_message(message)

        except Exception as e:
            logger.error(f"Failed to log significant change: {e}")

    def generate_performance_summary(self) -> Dict:
        """Generate overall performance summary"""
        try:
            summary = {
                'total_tokens_tracked': len(self.tracked_tokens),
                'active_tracking': 0,
                'completed_tracking': 0,
                'profitable_tokens': 0,
                'losing_tokens': 0,
                'total_gain_percent': 0,
                'total_loss_percent': 0,
                'best_performer': None,
                'worst_performer': None,
                'average_performance': 0,
                'generated_at': datetime.now().isoformat()
            }

            performances = []
            best_gain = float('-inf')
            worst_loss = float('inf')

            for token_address, tracking_data in self.tracked_tokens.items():
                status = tracking_data.get('status', 'tracking')
                performance = tracking_data.get('performance', {})

                if status == 'tracking':
                    summary['active_tracking'] += 1
                else:
                    summary['completed_tracking'] += 1

                price_change = performance.get('price_change_percent', 0)

                if price_change > 0:
                    summary['profitable_tokens'] += 1
                    summary['total_gain_percent'] += price_change
                elif price_change < 0:
                    summary['losing_tokens'] += 1
                    summary['total_loss_percent'] += abs(price_change)

                performances.append(price_change)

                # Track best and worst performers
                if price_change > best_gain:
                    best_gain = price_change
                    summary['best_performer'] = {
                        'token_address': token_address,
                        'gain_percent': price_change,
                        'multiplier': performance.get('price_change_multiplier', 1)
                    }

                if price_change < worst_loss:
                    worst_loss = price_change
                    summary['worst_performer'] = {
                        'token_address': token_address,
                        'loss_percent': price_change,
                        'multiplier': performance.get('price_change_multiplier', 1)
                    }

            # Calculate averages
            if performances:
                summary['average_performance'] = sum(performances) / len(performances)

            return summary

        except Exception as e:
            logger.error(f"Failed to generate performance summary: {e}")
            return {}

    def save_tracking_data(self):
        """Save tracking data to file"""
        try:
            save_json_data(self.tracked_tokens, self.tracking_file)
            save_json_data(self.performance_history, self.performance_file)
        except Exception as e:
            logger.error(f"Failed to save tracking data: {e}")

    def cleanup_old_tracking_data(self):
        """Remove old completed tracking entries"""
        try:
            cutoff_time = datetime.now() - timedelta(days=7)  # Keep 7 days of completed data

            tokens_to_remove = []
            for token_address, tracking_data in self.tracked_tokens.items():
                if tracking_data.get('status') == 'completed':
                    start_time = datetime.fromisoformat(tracking_data['start_time'])
                    if start_time < cutoff_time:
                        tokens_to_remove.append(token_address)

            for token_address in tokens_to_remove:
                del self.tracked_tokens[token_address]

            if tokens_to_remove:
                logger.info(f"Cleaned up {len(tokens_to_remove)} old tracking entries")
                self.save_tracking_data()

        except Exception as e:
            logger.error(f"Failed to cleanup old tracking data: {e}")

    def tracking_loop(self):
        """Main tracking loop that runs in background thread"""
        logger.info("PnL tracking loop started")

        while self.is_running:
            try:
                # Update all tracked tokens
                updated_count = 0
                for token_address in list(self.tracked_tokens.keys()):
                    if self.tracked_tokens[token_address].get('status') == 'tracking':
                        performance = self.update_token_performance(token_address)
                        if performance:
                            updated_count += 1

                if updated_count > 0:
                    logger.info(f"Updated performance for {updated_count} tokens")
                    self.save_tracking_data()

                # Periodic cleanup
                if time.time() % 3600 < 60:  # Once per hour
                    self.cleanup_old_tracking_data()

                # Wait for next check
                time.sleep(self.check_interval_minutes * 60)

            except Exception as e:
                logger.error(f"Error in tracking loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying

    def start_tracking(self):
        """Start the background tracking thread"""
        if self.is_running:
            logger.warning("Tracking already running")
            return

        self.is_running = True
        self.tracking_thread = threading.Thread(target=self.tracking_loop, daemon=True)
        self.tracking_thread.start()
        logger.info("PnL tracking started")

    def stop_tracking(self):
        """Stop the background tracking thread"""
        if not self.is_running:
            return

        self.is_running = False
        if self.tracking_thread:
            self.tracking_thread.join(timeout=5)

        self.save_tracking_data()
        logger.info("PnL tracking stopped")

# Global tracker instance
_tracker_instance = None

def get_tracker() -> PnLTracker:
    """Get or create global tracker instance"""
    global _tracker_instance
    if _tracker_instance is None:
        _tracker_instance = PnLTracker()
    return _tracker_instance

def start_pnl_tracking():
    """Start PnL tracking (called from main)"""
    tracker = get_tracker()
    tracker.start_tracking()

def add_token_for_tracking(token_address: str, analysis_data: Dict):
    """Add a token to PnL tracking"""
    tracker = get_tracker()
    tracker.add_token_for_tracking(token_address, analysis_data)

def get_performance_summary() -> Dict:
    """Get current performance summary"""
    tracker = get_tracker()
    return tracker.generate_performance_summary()