# Monaco v1.0

**Advanced Cryptocurrency Token Detection & Alert System with Telegram Signal Integration**

Monaco v1.0 is a sophisticated cryptocurrency detection engine that combines Solana blockchain monitoring with Telegram signal analysis from GMGN groups. It provides dual-source token detection, advanced analysis, and intelligent alert generation.

## 🚀 Key Features

### 🔗 **Dual-Source Detection**
- **Solana Blockchain Monitoring**: Direct RPC monitoring for new token creation events
- **Telegram Signal Integration**: Real-time monitoring of GMGN Signals group using Telethon
- **Cross-Reference Validation**: Validates signals across both sources for higher accuracy

### 📊 **Advanced Analysis Engine**
- **Multi-Factor Token Analysis**: Evaluates buy rate, market cap, liquidity, holder distribution
- **Trigger Pattern Detection**: Identifies "Advertised", "KOL Buy", "FDV Surge", "Smart Money FOMO" signals
- **Risk Assessment**: Comprehensive risk scoring with confidence levels
- **Signal Strength Calculation**: Combines multiple indicators for overall token scoring

### 🎯 **Operational Modes**
- **Test Mode**: Monitor and analyze without posting alerts (perfect for testing and tuning)
- **Live Mode**: Full functionality with real-time alert posting
- **Configurable Thresholds**: Customizable criteria for different market conditions

### 📱 **Enhanced Telegram Integration**
- **Bot Alerts**: Automated posting to target channels with rich formatting
- **User Session Monitoring**: Secure Telethon integration for group monitoring
- **Chart Previews**: Dexscreener chart thumbnails and preview cards
- **Rate Limiting Protection**: Intelligent throttling to avoid API limits

### 📈 **Performance Tracking**
- **Real-time PnL Monitoring**: Track token performance over configurable periods
- **Success Rate Analytics**: Monitor detection accuracy and profitability
- **Daily Recap Reports**: Automated performance summaries

## 📋 Requirements

- Python 3.8+
- Telegram Bot Token
- Internet connection for API access

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8+
- Telegram account
- Internet connection for API access

### 1. **Clone and Install**
```bash
git clone <repository-url>
cd Monaco_v1.0
pip install -r requirements.txt
```

### 2. **Telegram Bot Setup**
```bash
# Create bot with @BotFather on Telegram
# Get bot token and add to environment
cp .env.example .env
# Edit .env and add TELEGRAM_BOT_TOKEN
```

### 3. **Telethon User Session Setup**
For GMGN group monitoring, you need Telegram API credentials:

1. **Get API Credentials**:
   - Visit https://my.telegram.org
   - Log in with your Telegram account
   - Go to "API Development Tools"
   - Create a new application
   - Copy `api_id` and `api_hash`

2. **Configure Environment**:
   ```bash
   # Add to .env file:
   TELEGRAM_API_ID=your_api_id
   TELEGRAM_API_HASH=your_api_hash
   ```

3. **First-Time Authentication**:
   ```bash
   # Run Monaco - it will prompt for phone number and verification code
   python main.py
   # Follow the prompts to authenticate your Telegram account
   # Session will be saved for future use
   ```

### 4. **Configure Target Channel**
- Add your Telegram bot to your target channel as an admin
- Update `config.yaml` with your channel ID
- Ensure the bot has permission to post messages

## ⚙️ Configuration

### Operation Modes
```yaml
operation_mode:
  current_mode: "test"           # "test" or "live"

  test_mode:
    enabled: true
    post_alerts: false           # Monitor without posting
    log_detections: true         # Log all detections
    test_duration_hours: 24      # Test period

  live_mode:
    enabled: false
    post_alerts: true            # Post real alerts
    max_alerts_per_hour: 10      # Rate limiting
```

### Token Analysis Criteria
```yaml
token_analysis:
  min_buy_rate_per_min: 2        # Minimum buy transactions per minute
  max_initial_mc: 15000          # Maximum initial market cap in USD
  min_holders_ratio: 0.5         # Minimum unique holders ratio
  min_liquidity_usd: 1000        # Minimum liquidity in USD
```

### GMGN Monitoring
```yaml
gmgn_monitoring:
  enabled: true
  group_identifier: "gmgn_signals"  # Group to monitor
  process_all_messages: true
  ignore_old_messages: true         # Only new messages after startup
```

### Trigger Detection
```yaml
trigger_detection:
  patterns:
    advertised: ["advertised", "ad buy", "advertisement"]
    kol_buy: ["kol buy", "kol bought", "influencer buy"]
    fdv_surge: ["fdv surge", "fdv pump", "market cap surge"]
    smart_money: ["smart money", "whale buy", "smart money fomo"]
```

## 🚀 Usage

1. **Start the monitoring system**:
   ```bash
   python main.py
   ```

2. **Monitor logs**:
   ```bash
   tail -f logs/monaco.log
   ```

3. **Check performance**:
   - View `data/history.json` for detected tokens
   - Check `data/pnl_tracking.json` for performance data

## 📊 How It Works

### Dual-Source Detection Pipeline

1. **Solana Blockchain Monitoring**:
   - Monitors SPL Token Program for new token creation events
   - Extracts token metadata and initial transaction data
   - Validates token contracts and mint addresses

2. **Telegram Signal Processing**:
   - Connects to GMGN Signals group via Telethon user session
   - Parses messages for trigger patterns and contract addresses
   - Extracts signal strength and confidence metrics

3. **Signal Cross-Reference**:
   - Validates Telegram signals against blockchain data
   - Combines multiple signal sources for higher accuracy
   - Calculates combined confidence scores

4. **Advanced Token Analysis**:
   - Fetches real-time price and liquidity data from DexScreener
   - Evaluates trading activity and buy/sell patterns
   - Calculates risk scores based on multiple factors:
     - Market capitalization and liquidity
     - Trading volume and velocity
     - Holder distribution patterns
     - Signal source reliability

5. **Intelligent Alert Generation**:
   - Applies configurable thresholds and filters
   - Generates rich alerts with chart previews
   - Posts to Telegram with rate limiting protection
   - Includes confidence scores and reasoning

6. **Performance Tracking**:
   - Monitors token performance over configurable periods
   - Tracks success rates and profitability metrics
   - Generates daily performance reports

## 🔧 Advanced Configuration

### Solana RPC Endpoints
The system uses multiple RPC endpoints with automatic failover:
- Primary: `https://api.mainnet-beta.solana.com`
- Backup endpoints for redundancy

### Rate Limiting
Built-in rate limiting prevents API abuse:
- Solana RPC: 2 requests/second
- Telegram: 20 messages/minute
- DexScreener: Cached responses

### Error Handling
- Automatic endpoint rotation on failures
- Exponential backoff for retries
- Graceful degradation when services are unavailable

## 📁 Project Structure

```
Monaco_v1.0/
├── main.py                    # Entry point with mode switching
├── launch_watcher.py          # Dual-source token monitoring
├── coin_analyzer.py           # Advanced token analysis engine
├── autoposter.py              # Telegram bot integration
├── pnl_tracker.py             # Performance tracking
├── gmgn_parser.py             # Specialized GMGN message parser
├── trigger_detector.py        # Generic trigger detection (legacy)
├── dex_preview.py             # Chart generation
├── config.yaml                # Comprehensive configuration
├── requirements.txt           # Python dependencies
├── auth/                      # Authentication modules
│   ├── __init__.py
│   ├── session_manager.py     # Telethon session management
│   └── session_data.json      # Encrypted session storage
├── utils/
│   └── helpers.py             # Utility functions
├── data/                      # Data storage
│   ├── history.json           # Token detection history
│   ├── pnl_tracking.json      # Performance data
│   └── charts/                # Generated chart images
└── logs/                      # Log files
    └── monaco.log             # Application logs
```

## 🚨 Important Notes

- **API Limits**: The system respects API rate limits to avoid being blocked
- **Data Sources**: Uses free APIs (Solana RPC, DexScreener) - no paid subscriptions required
- **Risk Warning**: This is for informational purposes only, not financial advice
- **Testing**: Test with small amounts first to verify functionality

## 🔍 Troubleshooting

### Common Issues

1. **"Telegram bot token not configured"**:
   - Ensure `TELEGRAM_BOT_TOKEN` is set in your `.env` file
   - Verify the token is correct from @BotFather

2. **"No price data available"**:
   - Token might be too new or not listed on DEXes
   - Check if DexScreener API is accessible

3. **"Failed to get signatures"**:
   - Solana RPC endpoint might be down
   - System will automatically try backup endpoints

### Logs
Check `logs/monaco.log` for detailed error information and system status.

## 📈 Performance Metrics

The system tracks:
- Detection accuracy
- Alert response time
- Token performance after detection
- System uptime and error rates

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational and research purposes. Use responsibly and in accordance with all applicable laws and regulations.

## ⚠️ Disclaimer

This software is provided for informational purposes only. It does not constitute financial advice. Cryptocurrency trading involves substantial risk of loss. Always do your own research before making investment decisions.