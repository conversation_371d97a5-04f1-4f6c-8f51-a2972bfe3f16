# Monaco v1.0

**Advanced Cryptocurrency Token Detection & Alert System**

Monaco v1.0 is a sophisticated cryptocurrency detection engine that monitors Solana blockchain and Pump.fun for new token launches, analyzes their potential using advanced metrics, and automatically posts alerts to Telegram channels.

## 🚀 Features

- **Real-time Token Monitoring**: Monitors Solana blockchain for new token creation events
- **Advanced Token Analysis**: Evaluates tokens based on buy rate, market cap, liquidity, and holder distribution
- **Intelligent Risk Assessment**: Calculates comprehensive risk scores and investment recommendations
- **Telegram Integration**: Automated alerts with detailed token information and analysis
- **PnL Tracking**: Tracks performance of detected tokens over time
- **Rate Limiting Protection**: Built-in protection against API rate limits
- **Error Recovery**: Robust error handling with automatic recovery mechanisms

## 📋 Requirements

- Python 3.8+
- Telegram Bot Token
- Internet connection for API access

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd Monaco_v1.0
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env and add your Telegram bot token
   ```

4. **Set up Telegram Bot**:
   - Message @BotFather on Telegram
   - Create a new bot with `/newbot`
   - Copy the bot token to your `.env` file
   - Add your bot to your target channel as an admin

## ⚙️ Configuration

Edit `config.yaml` to customize detection criteria:

```yaml
token_analysis:
  min_buy_rate_per_min: 2        # Minimum buy transactions per minute
  max_initial_mc: 15000          # Maximum initial market cap in USD
  min_holders_ratio: 0.5         # Minimum unique holders ratio
  min_liquidity_usd: 1000        # Minimum liquidity in USD

telegram:
  channel_id: "@your_channel"    # Your Telegram channel
```

## 🚀 Usage

1. **Start the monitoring system**:
   ```bash
   python main.py
   ```

2. **Monitor logs**:
   ```bash
   tail -f logs/monaco.log
   ```

3. **Check performance**:
   - View `data/history.json` for detected tokens
   - Check `data/pnl_tracking.json` for performance data

## 📊 How It Works

1. **Token Detection**: Monitors Solana RPC endpoints for new token creation transactions
2. **Data Enrichment**: Fetches price, liquidity, and trading data from DexScreener API
3. **Analysis**: Evaluates tokens against configurable criteria:
   - Buy rate per minute
   - Market capitalization
   - Liquidity levels
   - Holder distribution
   - Trading volume
4. **Risk Assessment**: Calculates risk scores and generates recommendations
5. **Alert Generation**: Posts formatted alerts to Telegram for promising tokens
6. **Performance Tracking**: Monitors token performance over time

## 🔧 Advanced Configuration

### Solana RPC Endpoints
The system uses multiple RPC endpoints with automatic failover:
- Primary: `https://api.mainnet-beta.solana.com`
- Backup endpoints for redundancy

### Rate Limiting
Built-in rate limiting prevents API abuse:
- Solana RPC: 2 requests/second
- Telegram: 20 messages/minute
- DexScreener: Cached responses

### Error Handling
- Automatic endpoint rotation on failures
- Exponential backoff for retries
- Graceful degradation when services are unavailable

## 📁 Project Structure

```
Monaco_v1.0/
├── main.py              # Entry point
├── launch_watcher.py    # Token monitoring logic
├── coin_analyzer.py     # Token analysis engine
├── autoposter.py        # Telegram integration
├── pnl_tracker.py       # Performance tracking
├── config.yaml          # Configuration file
├── requirements.txt     # Python dependencies
├── utils/
│   └── helpers.py       # Utility functions
├── data/                # Data storage
│   ├── history.json     # Token history
│   └── pnl_tracking.json # Performance data
└── logs/                # Log files
    └── monaco.log       # Application logs
```

## 🚨 Important Notes

- **API Limits**: The system respects API rate limits to avoid being blocked
- **Data Sources**: Uses free APIs (Solana RPC, DexScreener) - no paid subscriptions required
- **Risk Warning**: This is for informational purposes only, not financial advice
- **Testing**: Test with small amounts first to verify functionality

## 🔍 Troubleshooting

### Common Issues

1. **"Telegram bot token not configured"**:
   - Ensure `TELEGRAM_BOT_TOKEN` is set in your `.env` file
   - Verify the token is correct from @BotFather

2. **"No price data available"**:
   - Token might be too new or not listed on DEXes
   - Check if DexScreener API is accessible

3. **"Failed to get signatures"**:
   - Solana RPC endpoint might be down
   - System will automatically try backup endpoints

### Logs
Check `logs/monaco.log` for detailed error information and system status.

## 📈 Performance Metrics

The system tracks:
- Detection accuracy
- Alert response time
- Token performance after detection
- System uptime and error rates

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational and research purposes. Use responsibly and in accordance with all applicable laws and regulations.

## ⚠️ Disclaimer

This software is provided for informational purposes only. It does not constitute financial advice. Cryptocurrency trading involves substantial risk of loss. Always do your own research before making investment decisions.