# Monaco v1.0 Configuration File

# Token Analysis Criteria
token_analysis:
  min_buy_rate_per_min: 2        # Minimum buy transactions per minute
  max_initial_mc: 15000          # Maximum initial market cap in USD
  min_holders_ratio: 0.5         # Minimum unique holders ratio
  min_liquidity_usd: 1000        # Minimum liquidity in USD
  max_age_minutes: 60            # Maximum token age in minutes for "new" tokens
  min_volume_24h: 5000           # Minimum 24h volume in USD

# Solana RPC Configuration
solana:
  # Primary RPC endpoints (free tier)
  rpc_endpoints:
    - "https://api.mainnet-beta.solana.com"
    - "https://solana-api.projectserum.com"
    - "https://rpc.ankr.com/solana"

  # Backup RPC endpoints
  backup_endpoints:
    - "https://api.mainnet-beta.solana.com"

  # RPC request settings
  timeout_seconds: 30
  max_retries: 3
  retry_delay_seconds: 2

# Telegram Bot Configuration
telegram:
  bot_token: ""                  # Set via environment variable TELEGRAM_BOT_TOKEN
  channel_id: "@drako1k"         # Target channel for alerts
  chat_id: ""                    # Alternative: use chat ID instead of channel

  # Rate limiting to avoid Telegram API limits
  max_messages_per_minute: 20
  max_messages_per_hour: 100

  # Message formatting
  enable_markdown: true
  include_charts: false          # Future feature for price charts

# Monitoring Settings
monitoring:
  poll_interval_seconds: 10      # How often to check for new tokens
  batch_size: 50                 # Number of transactions to process per batch
  max_concurrent_requests: 5     # Maximum concurrent API requests

  # Cache settings to reduce API calls
  cache_duration_minutes: 5
  max_cache_size: 1000

# Data Storage
storage:
  history_file: "data/history.json"
  backup_interval_hours: 24
  max_history_days: 30

# Logging Configuration
logging:
  level: "INFO"                  # DEBUG, INFO, WARNING, ERROR
  file: "logs/monaco.log"
  max_file_size_mb: 10
  backup_count: 5

# Performance Tracking
pnl_tracking:
  track_duration_hours: 24       # How long to track each token
  check_interval_minutes: 30     # How often to update PnL data
  min_price_change_percent: 5    # Minimum price change to log

# Error Handling
error_handling:
  max_consecutive_errors: 10     # Stop after this many consecutive errors
  error_cooldown_minutes: 5      # Wait time after hitting error limit
  notify_on_errors: true         # Send error notifications to Telegram

# Rate Limiting (to avoid being blocked)
rate_limiting:
  requests_per_second: 2         # Maximum requests per second to any API
  burst_limit: 10                # Maximum burst requests
  cooldown_on_limit: 60          # Seconds to wait if rate limited

# Telethon Configuration (User Client for Group Monitoring)
telethon:
  api_id: ""                     # Get from https://my.telegram.org (set via TELEGRAM_API_ID env var)
  api_hash: ""                   # Get from https://my.telegram.org (set via TELEGRAM_API_HASH env var)
  session_string: ""             # Generated after first login (set via TELEGRAM_SESSION_STRING env var)

  # Connection settings
  connection_retries: 5
  retry_delay: 5
  flood_sleep_threshold: 60

# GMGN Signals Group Monitoring
gmgn_monitoring:
  enabled: true
  group_identifier: "gmgn_signals"  # Group username or ID

  # Alternative groups to monitor (backup sources)
  backup_groups:
    - "crypto_signals_backup"
    - "solana_gems"

  # Message processing
  process_all_messages: true
  ignore_old_messages: true      # Only process new messages after startup
  max_message_age_minutes: 60    # Ignore messages older than this

# Trigger Detection Patterns
trigger_detection:
  enabled: true

  # Trigger patterns for different event types
  patterns:
    advertised:
      - "advertised"
      - "ad buy"
      - "advertisement"
      - "promoted"

    kol_buy:
      - "kol buy"
      - "kol bought"
      - "influencer buy"
      - "influencer bought"
      - "kol signal"

    fdv_surge:
      - "fdv surge"
      - "fdv pump"
      - "market cap surge"
      - "mcap pump"
      - "fdv spike"

    smart_money:
      - "smart money"
      - "whale buy"
      - "smart money fomo"
      - "whale signal"
      - "smart wallet"

    volume_spike:
      - "volume spike"
      - "volume surge"
      - "high volume"
      - "volume pump"
      - "vol spike"

    price_pump:
      - "price pump"
      - "pumping"
      - "mooning"
      - "price surge"
      - "breakout"

  # Minimum confidence threshold for triggers
  min_confidence: 0.6

  # Contract address extraction settings
  ca_extraction:
    enabled: true
    validate_addresses: true
    blacklist_common_tokens: true

# Operational Modes
operation_mode:
  current_mode: "test"           # "test" or "live"

  test_mode:
    enabled: true
    post_alerts: false           # Don't post to Telegram in test mode
    log_detections: true         # Log all detections for analysis
    save_test_data: true         # Save test results to file
    test_duration_hours: 24      # How long to run in test mode

  live_mode:
    enabled: false
    post_alerts: true            # Post real alerts to Telegram
    require_confirmation: false  # Require manual confirmation before posting
    max_alerts_per_hour: 10      # Limit alerts to prevent spam

# Dex Preview Configuration
dex_preview:
  enabled: true
  chart_width: 800
  chart_height: 400
  thumbnail_size: [400, 200]

  # Chart generation settings
  include_charts: true
  chart_timeframe: "1h"
  save_charts: true
  cleanup_old_charts: true
  max_chart_age_hours: 24

# Signal Validation and Cross-Reference
signal_validation:
  enabled: true

  # Cross-reference Telegram signals with blockchain data
  cross_reference_blockchain: true

  # Require both Telegram signal AND blockchain detection
  require_dual_confirmation: false

  # Signal scoring weights
  scoring:
    telegram_signal_weight: 0.4
    blockchain_detection_weight: 0.6
    trigger_confidence_weight: 0.3
    ca_validation_weight: 0.7

  # Minimum combined score to trigger alert
  min_combined_score: 0.7

# Enhanced Alert Configuration
enhanced_alerts:
  enabled: true

  # Include preview images in alerts
  include_preview_images: true
  include_chart_thumbnails: true

  # Alert content customization
  include_trigger_info: true
  include_signal_source: true
  include_confidence_score: true

  # Recap posting (summary of daily performance)
  daily_recap:
    enabled: true
    post_time: "23:00"           # UTC time to post daily recap
    include_performance_stats: true
    include_best_performers: true
