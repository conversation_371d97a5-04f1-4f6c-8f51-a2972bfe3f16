# Monaco v1.0 Configuration File

# Token Analysis Criteria
token_analysis:
  min_buy_rate_per_min: 2        # Minimum buy transactions per minute
  max_initial_mc: 15000          # Maximum initial market cap in USD
  min_holders_ratio: 0.5         # Minimum unique holders ratio
  min_liquidity_usd: 1000        # Minimum liquidity in USD
  max_age_minutes: 60            # Maximum token age in minutes for "new" tokens
  min_volume_24h: 5000           # Minimum 24h volume in USD

# Solana RPC Configuration
solana:
  # Primary RPC endpoints (free tier)
  rpc_endpoints:
    - "https://api.mainnet-beta.solana.com"
    - "https://solana-api.projectserum.com"
    - "https://rpc.ankr.com/solana"

  # Backup RPC endpoints
  backup_endpoints:
    - "https://api.mainnet-beta.solana.com"

  # RPC request settings
  timeout_seconds: 30
  max_retries: 3
  retry_delay_seconds: 2

# Telegram Bot Configuration
telegram:
  bot_token: ""                  # Set via environment variable TELEGRAM_BOT_TOKEN
  channel_id: "@drako1k"         # Target channel for alerts
  chat_id: ""                    # Alternative: use chat ID instead of channel

  # Rate limiting to avoid Telegram API limits
  max_messages_per_minute: 20
  max_messages_per_hour: 100

  # Message formatting
  enable_markdown: true
  include_charts: false          # Future feature for price charts

# Monitoring Settings
monitoring:
  poll_interval_seconds: 10      # How often to check for new tokens
  batch_size: 50                 # Number of transactions to process per batch
  max_concurrent_requests: 5     # Maximum concurrent API requests

  # Cache settings to reduce API calls
  cache_duration_minutes: 5
  max_cache_size: 1000

# Data Storage
storage:
  history_file: "data/history.json"
  backup_interval_hours: 24
  max_history_days: 30

# Logging Configuration
logging:
  level: "INFO"                  # DEBUG, INFO, WARNING, ERROR
  file: "logs/monaco.log"
  max_file_size_mb: 10
  backup_count: 5

# Performance Tracking
pnl_tracking:
  track_duration_hours: 24       # How long to track each token
  check_interval_minutes: 30     # How often to update PnL data
  min_price_change_percent: 5    # Minimum price change to log

# Error Handling
error_handling:
  max_consecutive_errors: 10     # Stop after this many consecutive errors
  error_cooldown_minutes: 5      # Wait time after hitting error limit
  notify_on_errors: true         # Send error notifications to Telegram

# Rate Limiting (to avoid being blocked)
rate_limiting:
  requests_per_second: 2         # Maximum requests per second to any API
  burst_limit: 10                # Maximum burst requests
  cooldown_on_limit: 60          # Seconds to wait if rate limited
