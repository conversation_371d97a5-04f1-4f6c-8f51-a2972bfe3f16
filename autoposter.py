"""
Monaco v1.0 - Auto Poster
Telegram bot integration for posting token alerts with rate limiting and error handling
"""

import logging
import time
import asyncio
from typing import Dict, Optional, List
from datetime import datetime
import telegram
from telegram import Bo<PERSON>
from telegram.error import TelegramError, RetryAfter, TimedOut
from utils.helpers import (
    ConfigManager, RateLimiter, ErrorHandler,
    format_number, format_percentage
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelegramPoster:
    """Handles Telegram message posting with rate limiting and error recovery"""

    def __init__(self):
        self.config = ConfigManager()
        self.bot_token = self.config.get('telegram.bot_token')
        self.channel_id = self.config.get('telegram.channel_id', '@drako1k')
        self.chat_id = self.config.get('telegram.chat_id')

        # Initialize bot
        if not self.bot_token:
            logger.error("Telegram bot token not configured! Set TELEGRAM_BOT_TOKEN environment variable.")
            self.bot = None
        else:
            self.bot = Bot(token=self.bot_token)

        # Rate limiting to avoid Telegram API limits
        self.rate_limiter = RateLimiter(
            max_calls=self.config.get('telegram.max_messages_per_minute', 20),
            time_window=60
        )

        self.hourly_limiter = RateLimiter(
            max_calls=self.config.get('telegram.max_messages_per_hour', 100),
            time_window=3600
        )

        # Error handling
        self.error_handler = ErrorHandler(max_consecutive_errors=5)

        # Message formatting settings
        self.enable_markdown = self.config.get('telegram.enable_markdown', True)

        # Track sent messages to avoid duplicates
        self.sent_tokens = set()
        self.last_cleanup = time.time()

        logger.info(f"TelegramPoster initialized for channel: {self.channel_id}")

    def format_alert_message(self, token_data: Dict, analysis_result: Dict) -> str:
        """Format token alert message for Telegram"""
        try:
            token_address = token_data.get('mint') or token_data.get('address', 'Unknown')

            # Extract key data
            price_data = analysis_result.get('price_data', {})
            trading_data = analysis_result.get('trading_analysis', {})
            risk_data = analysis_result.get('risk_analysis', {})
            recommendation = analysis_result.get('recommendation', {})

            # Build message
            if self.enable_markdown:
                message = f"🚀 *NEW TOKEN ALERT* 🚀\n\n"
                message += f"📍 *Address:* `{token_address}`\n"

                # Price and market data
                if price_data:
                    price = price_data.get('price_usd', 0)
                    market_cap = price_data.get('market_cap', 0)
                    liquidity = price_data.get('liquidity_usd', 0)
                    volume_24h = price_data.get('volume_24h', 0)

                    message += f"💰 *Price:* ${price:.8f}\n"
                    if market_cap > 0:
                        message += f"📊 *Market Cap:* ${format_number(market_cap)}\n"
                    message += f"💧 *Liquidity:* ${format_number(liquidity)}\n"
                    message += f"📈 *24h Volume:* ${format_number(volume_24h)}\n"

                # Trading metrics
                if trading_data:
                    buy_rate = trading_data.get('buy_rate_per_min', 0)
                    trading_score = trading_data.get('trading_score', 0)

                    message += f"⚡ *Buy Rate:* {buy_rate:.1f}/min\n"
                    message += f"🎯 *Trading Score:* {trading_score:.1f}/100\n"

                # Risk assessment
                if risk_data:
                    risk_score = risk_data.get('risk_score', 0)
                    risk_level = risk_data.get('risk_level', 'UNKNOWN')

                    risk_emoji = {'LOW': '🟢', 'MEDIUM': '🟡', 'HIGH': '🟠', 'VERY_HIGH': '🔴'}.get(risk_level, '⚪')
                    message += f"{risk_emoji} *Risk Level:* {risk_level} ({risk_score}/100)\n"

                # Recommendation
                if recommendation:
                    rec = recommendation.get('recommendation', 'UNKNOWN')
                    confidence = recommendation.get('confidence', 'LOW')
                    overall_score = recommendation.get('overall_score', 0)

                    rec_emoji = {
                        'STRONG_BUY': '🚀🚀', 'BUY': '🚀',
                        'HOLD': '⏸️', 'AVOID': '❌'
                    }.get(rec, '❓')

                    message += f"\n{rec_emoji} *Recommendation:* {rec}\n"
                    message += f"🎯 *Confidence:* {confidence}\n"
                    message += f"⭐ *Overall Score:* {overall_score:.1f}/100\n"

                # Reasoning
                reasoning = recommendation.get('reasoning', '')
                if reasoning and len(reasoning) < 200:
                    message += f"\n💡 *Analysis:* {reasoning}\n"

                # Links
                message += f"\n🔗 *Links:*\n"
                message += f"• [DexScreener](https://dexscreener.com/solana/{token_address})\n"
                message += f"• [Pump.fun](https://pump.fun/{token_address})\n"

                # Timestamp
                timestamp = datetime.now().strftime("%H:%M:%S UTC")
                message += f"\n⏰ *Detected:* {timestamp}"

            else:
                # Plain text version
                message = f"🚀 NEW TOKEN ALERT 🚀\n\n"
                message += f"Address: {token_address}\n"

                if price_data:
                    message += f"Price: ${price_data.get('price_usd', 0):.8f}\n"
                    message += f"Market Cap: ${format_number(price_data.get('market_cap', 0))}\n"
                    message += f"Liquidity: ${format_number(price_data.get('liquidity_usd', 0))}\n"

                if recommendation:
                    message += f"Recommendation: {recommendation.get('recommendation', 'UNKNOWN')}\n"

                message += f"\nDexScreener: https://dexscreener.com/solana/{token_address}"

            return message

        except Exception as e:
            logger.error(f"Failed to format alert message: {e}")
            return f"🚀 NEW TOKEN ALERT 🚀\nAddress: {token_address}\nError formatting details."

    async def send_message_async(self, message: str, target: Optional[str] = None) -> bool:
        """Send message asynchronously with error handling"""
        if not self.bot:
            logger.error("Telegram bot not initialized")
            return False

        try:
            # Apply rate limiting
            self.rate_limiter.wait_if_needed()
            self.hourly_limiter.wait_if_needed()

            # Determine target
            chat_target = target or self.chat_id or self.channel_id

            # Send message
            parse_mode = 'Markdown' if self.enable_markdown else None

            await self.bot.send_message(
                chat_id=chat_target,
                text=message,
                parse_mode=parse_mode,
                disable_web_page_preview=True
            )

            logger.info(f"Message sent successfully to {chat_target}")
            self.error_handler.reset_error_count()
            return True

        except RetryAfter as e:
            logger.warning(f"Rate limited by Telegram, waiting {e.retry_after} seconds")
            await asyncio.sleep(e.retry_after)
            return False

        except TimedOut:
            logger.warning("Telegram request timed out")
            return False

        except TelegramError as e:
            logger.error(f"Telegram error: {e}")
            self.error_handler.handle_error(e, "send_message")
            return False

        except Exception as e:
            logger.error(f"Unexpected error sending message: {e}")
            self.error_handler.handle_error(e, "send_message")
            return False

    def send_message_sync(self, message: str, target: Optional[str] = None) -> bool:
        """Synchronous wrapper for sending messages"""
        try:
            # Create event loop if none exists
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Run async function
            return loop.run_until_complete(self.send_message_async(message, target))

        except Exception as e:
            logger.error(f"Failed to send message synchronously: {e}")
            return False

    def cleanup_sent_tokens(self):
        """Clean up old sent tokens to prevent memory bloat"""
        try:
            current_time = time.time()

            # Clean up every hour
            if current_time - self.last_cleanup > 3600:
                # Keep only recent tokens (last 24 hours worth)
                # This is a simple implementation - in production you might want more sophisticated tracking
                if len(self.sent_tokens) > 1000:
                    # Keep only the most recent 500
                    self.sent_tokens = set(list(self.sent_tokens)[-500:])

                self.last_cleanup = current_time
                logger.debug(f"Cleaned up sent tokens cache, now tracking {len(self.sent_tokens)} tokens")

        except Exception as e:
            logger.error(f"Failed to cleanup sent tokens: {e}")

    def should_send_alert(self, token_address: str) -> bool:
        """Check if we should send an alert for this token"""
        try:
            # Check if already sent
            if token_address in self.sent_tokens:
                logger.debug(f"Alert already sent for {token_address}")
                return False

            # Check error handler state
            if not self.error_handler.should_continue():
                logger.warning("Too many consecutive errors, skipping alert")
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking if should send alert: {e}")
            return False

    def post_token_alert(self, token_data: Dict, analysis_result: Dict) -> bool:
        """Post a token alert to Telegram"""
        try:
            token_address = token_data.get('mint') or token_data.get('address')
            if not token_address:
                logger.warning("Cannot post alert: missing token address")
                return False

            # Check if we should send this alert
            if not self.should_send_alert(token_address):
                return False

            # Format message
            message = self.format_alert_message(token_data, analysis_result)

            # Send message
            success = self.send_message_sync(message)

            if success:
                # Mark as sent
                self.sent_tokens.add(token_address)
                logger.info(f"Successfully posted alert for token: {token_address}")

                # Periodic cleanup
                self.cleanup_sent_tokens()

            return success

        except Exception as e:
            logger.error(f"Failed to post token alert: {e}")
            self.error_handler.handle_error(e, "post_token_alert")
            return False

# Global poster instance
_poster_instance = None

def get_poster() -> TelegramPoster:
    """Get or create global poster instance"""
    global _poster_instance
    if _poster_instance is None:
        _poster_instance = TelegramPoster()
    return _poster_instance

def post_alert(token_data: Dict, analysis_result: Dict) -> bool:
    """Main function called by launch_watcher to post alerts"""
    try:
        poster = get_poster()
        return poster.post_token_alert(token_data, analysis_result)
    except Exception as e:
        logger.error(f"Failed to post alert: {e}")
        return False

def send_status_message(message: str) -> bool:
    """Send a status/error message to Telegram"""
    try:
        poster = get_poster()
        return poster.send_message_sync(f"🤖 Monaco Bot Status: {message}")
    except Exception as e:
        logger.error(f"Failed to send status message: {e}")
        return False