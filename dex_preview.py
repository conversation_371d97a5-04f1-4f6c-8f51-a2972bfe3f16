"""
Monaco v1.0 - Dex Preview Generator
Generate Dexscreener chart thumbnails and previews for enhanced alert messages
"""

import os
import logging
import requests
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from PIL import Image, ImageDraw, ImageFont
import io
import base64
from utils.helpers import ConfigManager, cached_request

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DexPreviewGenerator:
    """Generates chart previews and thumbnails for token alerts"""
    
    def __init__(self):
        self.config = ConfigManager()
        
        # Chart configuration
        self.chart_width = self.config.get('dex_preview.chart_width', 800)
        self.chart_height = self.config.get('dex_preview.chart_height', 400)
        self.thumbnail_size = self.config.get('dex_preview.thumbnail_size', (400, 200))
        
        # Output directory
        self.output_dir = "data/charts"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Chart styling
        plt.style.use('dark_background')
        
        logger.info("DexPreviewGenerator initialized")
    
    @cached_request("dex_chart_data", ttl=300)  # Cache for 5 minutes
    def get_chart_data(self, token_address: str, timeframe: str = "1h") -> Optional[Dict]:
        """Get chart data from DexScreener API"""
        try:
            url = f"https://api.dexscreener.com/latest/dex/tokens/{token_address}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                pairs = data.get('pairs', [])
                
                if pairs:
                    # Get the most liquid pair
                    best_pair = max(pairs, key=lambda p: float(p.get('liquidity', {}).get('usd', 0)))
                    
                    # Extract OHLCV data if available
                    chart_data = {
                        'pair_address': best_pair.get('pairAddress'),
                        'token_address': token_address,
                        'current_price': float(best_pair.get('priceUsd', 0)),
                        'price_change_24h': float(best_pair.get('priceChange', {}).get('h24', 0)),
                        'volume_24h': float(best_pair.get('volume', {}).get('h24', 0)),
                        'liquidity': float(best_pair.get('liquidity', {}).get('usd', 0)),
                        'market_cap': float(best_pair.get('marketCap', 0)),
                        'dex': best_pair.get('dexId', 'unknown'),
                        'base_token': best_pair.get('baseToken', {}),
                        'quote_token': best_pair.get('quoteToken', {})
                    }
                    
                    return chart_data
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get chart data for {token_address}: {e}")
            return None
    
    def generate_price_chart(self, token_address: str, chart_data: Dict) -> Optional[str]:
        """Generate a price chart for the token"""
        try:
            # Create figure
            fig, ax = plt.subplots(figsize=(self.chart_width/100, self.chart_height/100), dpi=100)
            fig.patch.set_facecolor('#1a1a1a')
            ax.set_facecolor('#1a1a1a')
            
            # Since we don't have historical OHLC data from the basic API,
            # we'll create a simple info chart with current metrics
            
            current_price = chart_data.get('current_price', 0)
            price_change_24h = chart_data.get('price_change_24h', 0)
            volume_24h = chart_data.get('volume_24h', 0)
            liquidity = chart_data.get('liquidity', 0)
            market_cap = chart_data.get('market_cap', 0)
            
            # Clear the axes for a custom layout
            ax.clear()
            ax.set_xlim(0, 10)
            ax.set_ylim(0, 10)
            ax.axis('off')
            
            # Token info
            token_name = chart_data.get('base_token', {}).get('name', 'Unknown Token')
            token_symbol = chart_data.get('base_token', {}).get('symbol', 'UNK')
            
            # Title
            ax.text(5, 9, f"{token_name} ({token_symbol})", 
                   fontsize=20, fontweight='bold', ha='center', color='white')
            
            # Current price
            price_color = '#00ff00' if price_change_24h >= 0 else '#ff0000'
            ax.text(5, 7.5, f"${current_price:.8f}", 
                   fontsize=24, fontweight='bold', ha='center', color=price_color)
            
            # 24h change
            change_text = f"{price_change_24h:+.2f}%"
            ax.text(5, 6.5, change_text, 
                   fontsize=16, ha='center', color=price_color)
            
            # Metrics grid
            metrics = [
                ("Volume 24h", f"${self._format_number(volume_24h)}"),
                ("Liquidity", f"${self._format_number(liquidity)}"),
                ("Market Cap", f"${self._format_number(market_cap)}"),
                ("DEX", chart_data.get('dex', 'Unknown').upper())
            ]
            
            y_pos = 5
            for i, (label, value) in enumerate(metrics):
                x_pos = 2.5 if i % 2 == 0 else 7.5
                if i >= 2:
                    y_pos = 3.5
                
                ax.text(x_pos, y_pos, label, fontsize=12, ha='center', color='#888888')
                ax.text(x_pos, y_pos - 0.5, value, fontsize=14, fontweight='bold', ha='center', color='white')
            
            # Timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M UTC")
            ax.text(5, 1, f"Generated: {timestamp}", fontsize=10, ha='center', color='#666666')
            
            # Save chart
            filename = f"chart_{token_address}_{int(datetime.now().timestamp())}.png"
            filepath = os.path.join(self.output_dir, filename)
            
            plt.tight_layout()
            plt.savefig(filepath, facecolor='#1a1a1a', edgecolor='none', bbox_inches='tight', dpi=100)
            plt.close()
            
            logger.info(f"Generated chart: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Failed to generate chart for {token_address}: {e}")
            return None
    
    def create_thumbnail(self, chart_path: str) -> Optional[str]:
        """Create a thumbnail from the chart"""
        try:
            # Open and resize image
            with Image.open(chart_path) as img:
                img.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)
                
                # Save thumbnail
                thumbnail_path = chart_path.replace('.png', '_thumb.png')
                img.save(thumbnail_path, 'PNG')
                
                logger.info(f"Created thumbnail: {thumbnail_path}")
                return thumbnail_path
                
        except Exception as e:
            logger.error(f"Failed to create thumbnail: {e}")
            return None
    
    def generate_preview_card(self, token_address: str, analysis_data: Dict) -> Optional[str]:
        """Generate a comprehensive preview card with token information"""
        try:
            # Create a larger image for the card
            card_width, card_height = 600, 400
            img = Image.new('RGB', (card_width, card_height), color='#1a1a1a')
            draw = ImageDraw.Draw(img)
            
            # Try to load a font (fallback to default if not available)
            try:
                title_font = ImageFont.truetype("arial.ttf", 24)
                header_font = ImageFont.truetype("arial.ttf", 18)
                text_font = ImageFont.truetype("arial.ttf", 14)
                small_font = ImageFont.truetype("arial.ttf", 12)
            except:
                title_font = ImageFont.load_default()
                header_font = ImageFont.load_default()
                text_font = ImageFont.load_default()
                small_font = ImageFont.load_default()
            
            # Extract data
            price_data = analysis_data.get('price_data', {})
            trading_data = analysis_data.get('trading_analysis', {})
            risk_data = analysis_data.get('risk_analysis', {})
            recommendation = analysis_data.get('recommendation', {})
            
            # Header
            draw.text((20, 20), "Monaco v1.0 - Token Analysis", fill='white', font=header_font)
            
            # Token address (shortened)
            short_address = f"{token_address[:8]}...{token_address[-8:]}"
            draw.text((20, 50), f"CA: {short_address}", fill='#888888', font=text_font)
            
            # Price info
            current_price = price_data.get('price_usd', 0)
            price_change = price_data.get('price_change_24h', 0)
            price_color = '#00ff00' if price_change >= 0 else '#ff0000'
            
            draw.text((20, 80), f"Price: ${current_price:.8f}", fill=price_color, font=title_font)
            draw.text((20, 110), f"24h: {price_change:+.2f}%", fill=price_color, font=text_font)
            
            # Key metrics
            y_offset = 150
            metrics = [
                ("Market Cap", f"${self._format_number(price_data.get('market_cap', 0))}"),
                ("Liquidity", f"${self._format_number(price_data.get('liquidity_usd', 0))}"),
                ("Volume 24h", f"${self._format_number(price_data.get('volume_24h', 0))}"),
                ("Buy Rate", f"{trading_data.get('buy_rate_per_min', 0):.1f}/min"),
                ("Risk Score", f"{risk_data.get('risk_score', 0)}/100"),
                ("Recommendation", recommendation.get('recommendation', 'UNKNOWN'))
            ]
            
            for i, (label, value) in enumerate(metrics):
                x_pos = 20 if i % 2 == 0 else 320
                y_pos = y_offset + (i // 2) * 30
                
                draw.text((x_pos, y_pos), f"{label}:", fill='#888888', font=text_font)
                draw.text((x_pos + 120, y_pos), value, fill='white', font=text_font)
            
            # Footer
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M UTC")
            draw.text((20, card_height - 30), f"Generated: {timestamp}", fill='#666666', font=small_font)
            
            # Save card
            filename = f"card_{token_address}_{int(datetime.now().timestamp())}.png"
            filepath = os.path.join(self.output_dir, filename)
            img.save(filepath, 'PNG')
            
            logger.info(f"Generated preview card: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Failed to generate preview card: {e}")
            return None
    
    def _format_number(self, num: float) -> str:
        """Format number with appropriate suffixes"""
        try:
            if num >= 1_000_000_000:
                return f"{num / 1_000_000_000:.2f}B"
            elif num >= 1_000_000:
                return f"{num / 1_000_000:.2f}M"
            elif num >= 1_000:
                return f"{num / 1_000:.2f}K"
            else:
                return f"{num:.2f}"
        except:
            return str(num)
    
    def cleanup_old_charts(self, max_age_hours: int = 24):
        """Clean up old chart files"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            
            for filename in os.listdir(self.output_dir):
                filepath = os.path.join(self.output_dir, filename)
                
                if os.path.isfile(filepath):
                    file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                    
                    if file_time < cutoff_time:
                        os.remove(filepath)
                        logger.debug(f"Removed old chart: {filename}")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old charts: {e}")

# Global preview generator instance
_preview_generator = None

def get_preview_generator() -> DexPreviewGenerator:
    """Get or create global preview generator instance"""
    global _preview_generator
    if _preview_generator is None:
        _preview_generator = DexPreviewGenerator()
    return _preview_generator

def generate_token_preview(token_address: str, analysis_data: Dict) -> Optional[Dict]:
    """Generate preview materials for a token"""
    try:
        generator = get_preview_generator()
        
        # Get chart data
        chart_data = generator.get_chart_data(token_address)
        if not chart_data:
            logger.warning(f"No chart data available for {token_address}")
            return None
        
        # Generate chart
        chart_path = generator.generate_price_chart(token_address, chart_data)
        
        # Generate thumbnail
        thumbnail_path = None
        if chart_path:
            thumbnail_path = generator.create_thumbnail(chart_path)
        
        # Generate preview card
        card_path = generator.generate_preview_card(token_address, analysis_data)
        
        return {
            'chart_path': chart_path,
            'thumbnail_path': thumbnail_path,
            'card_path': card_path,
            'chart_data': chart_data
        }
        
    except Exception as e:
        logger.error(f"Failed to generate token preview: {e}")
        return None
