"""
Monaco v1.0 - GMG<PERSON> Parser
Specialized parser for GMGN Signals messages with enhanced CA extraction and alert detection
"""

import re
import logging
from typing import Dict, List, Optional
from datetime import datetime
from utils.helpers import ConfigManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def is_valid_ca(address: str) -> bool:
    """Validate if string is a valid Solana contract address"""
    try:
        # Basic validation for Solana addresses
        if not address or len(address) < 32 or len(address) > 44:
            return False
        
        # Check if it contains only valid base58 characters
        valid_chars = set('123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz')
        if not all(c in valid_chars for c in address):
            return False
        
        # Blacklist common non-token addresses
        blacklisted = {
            'So11111111111111111111111111111111111111112',  # Wrapped SOL
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',  # USDT
            '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',  # RAY
        }
        
        return address not in blacklisted
        
    except Exception:
        return False

def extract_contract_addresses(message: str) -> List[str]:
    """Extract contract addresses from GMGN message"""
    try:
        # Extract from gmgn.ai token/address URLs
        ca_candidates = re.findall(r'https:\/\/gmgn\.ai\/sol\/(?:token|address)\/([1-9A-HJ-NP-Za-km-z]{32,44})', message)
        
        # Extract any raw CA-looking strings
        base_candidates = re.findall(r'\b([1-9A-HJ-NP-Za-km-z]{32,44})\b', message)
        
        # Combine and validate
        ca_set = set()
        for ca in ca_candidates + base_candidates:
            if is_valid_ca(ca):
                ca_set.add(ca)
        
        return list(ca_set)
        
    except Exception as e:
        logger.error(f"Error extracting contract addresses: {e}")
        return []

def detect_alert_type(message: str) -> str:
    """Detect the type of GMGN alert from message content"""
    try:
        message_lower = message.lower()
        
        # Check for specific GMGN alert patterns
        if "kol" in message_lower and "buy" in message_lower:
            return "KOL FOMO"
        
        if "smart money" in message_lower:
            return "Smart Money"
        
        if "fdv surge" in message_lower or "市值飙升" in message_lower:
            return "FDV Surge"
        
        if "advertised on dexscreener" in message_lower:
            return "DEX Ad"
        
        if "ct0" in message_lower or "cto" in message_lower:
            return "CTO Alert"
        
        if "update dexscreener" in message_lower:
            return "DEX Screener Social Update"
        
        if "pump已满" in message_lower or "pump completed" in message_lower:
            return "Pump Completed"
        
        if "king of the hill" in message_lower or "koth" in message_lower:
            return "Pump KOTH"
        
        # Additional patterns for common GMGN alerts
        if "new listing" in message_lower:
            return "New Listing"
        
        if "volume spike" in message_lower:
            return "Volume Spike"
        
        if "price surge" in message_lower:
            return "Price Surge"
        
        return "Unknown"
        
    except Exception as e:
        logger.error(f"Error detecting alert type: {e}")
        return "Unknown"

def parse_token_metadata(message: str) -> Dict:
    """Parse token metadata from GMGN message"""
    try:
        metadata = {}
        
        # Extract token symbol and name: $SYMBOL (Token Name)
        name_match = re.search(r'\$(\w+)\s*\(([^\)]+)\)', message)
        if name_match:
            metadata['symbol'] = name_match.group(1)
            metadata['name'] = name_match.group(2)
        
        # Extract market cap: MCP: $123.45K
        mcp_match = re.search(r'MCP: \$([\d.,]+[KMB]?)', message)
        if mcp_match:
            metadata['mcp'] = mcp_match.group(1)
        
        # Extract liquidity: Liq: ... ($123.45K)
        liq_match = re.search(r'Liq: .*?\(\$([\d.,KMB]+)', message)
        if liq_match:
            metadata['liquidity'] = liq_match.group(1)
        
        # Extract holder count: Holder: 123
        holders_match = re.search(r'Holder: (\d+)', message)
        if holders_match:
            metadata['holders'] = int(holders_match.group(1))
        
        # Extract open time: Open: 1.5h ago
        open_time_match = re.search(r'Open: ([\d.]+[hm]) ago', message)
        if open_time_match:
            metadata['open_time'] = open_time_match.group(1)
        
        # Extract price information if available
        price_match = re.search(r'Price: \$([\d.,]+)', message)
        if price_match:
            metadata['price'] = price_match.group(1)
        
        # Extract percentage changes
        change_match = re.search(r'([+-]?\d+\.?\d*%)', message)
        if change_match:
            metadata['price_change'] = change_match.group(1)
        
        return metadata
        
    except Exception as e:
        logger.error(f"Error parsing token metadata: {e}")
        return {}

def calculate_signal_strength(alert_type: str, metadata: Dict) -> float:
    """Calculate signal strength based on alert type and metadata"""
    try:
        base_scores = {
            "KOL FOMO": 0.8,
            "Smart Money": 0.9,
            "FDV Surge": 0.7,
            "DEX Ad": 0.6,
            "CTO Alert": 0.5,
            "Pump Completed": 0.4,
            "Pump KOTH": 0.8,
            "New Listing": 0.6,
            "Volume Spike": 0.7,
            "Price Surge": 0.7,
            "Unknown": 0.3
        }
        
        score = base_scores.get(alert_type, 0.3)
        
        # Boost score based on metadata quality
        if metadata.get('holders', 0) > 100:
            score += 0.1
        
        if metadata.get('mcp'):
            score += 0.05
        
        if metadata.get('liquidity'):
            score += 0.05
        
        # Cap at 1.0
        return min(score, 1.0)
        
    except Exception as e:
        logger.error(f"Error calculating signal strength: {e}")
        return 0.3

def gmgn_message_parser(raw_msg: str) -> List[Dict]:
    """
    Main GMGN message parser function
    Returns list of parsed token alerts from the message
    """
    try:
        # Extract contract addresses
        cas = extract_contract_addresses(raw_msg)
        
        if not cas:
            logger.debug("No valid contract addresses found in message")
            return []
        
        # Detect alert type
        alert_type = detect_alert_type(raw_msg)
        
        # Parse metadata
        metadata = parse_token_metadata(raw_msg)
        
        # Calculate signal strength
        signal_strength = calculate_signal_strength(alert_type, metadata)
        
        # Create timestamp
        timestamp = datetime.utcnow().isoformat()
        
        # Create result for each CA found
        results = []
        for ca in cas:
            result = {
                "ca": ca,
                "contract_address": ca,  # Alias for compatibility
                "alert_type": alert_type,
                "metadata": metadata,
                "signal_strength": signal_strength,
                "source": "GMGN",
                "timestamp": timestamp,
                "raw_message": raw_msg[:500],  # Truncate for storage
                "confidence": signal_strength  # Alias for compatibility
            }
            results.append(result)
            
        logger.info(f"GMGN parser extracted {len(results)} token alerts from message")
        return results
        
    except Exception as e:
        logger.error(f"Error in GMGN message parser: {e}")
        return []

def is_gmgn_message(message: str) -> bool:
    """Check if a message appears to be from GMGN based on patterns"""
    try:
        # Look for GMGN-specific indicators
        gmgn_indicators = [
            "gmgn.ai",
            "MCP:",
            "Holder:",
            "Open:",
            "kol buy",
            "smart money",
            "fdv surge",
            "pump已满",
            "king of the hill"
        ]
        
        message_lower = message.lower()
        return any(indicator.lower() in message_lower for indicator in gmgn_indicators)
        
    except Exception:
        return False

class GMGNParser:
    """Enhanced GMGN Parser class with duplicate detection, PnL tracking, and debug logging"""

    def __init__(self):
        self.config = ConfigManager()

        # Load history for duplicate detection
        self.history_file = "data/history.json"
        self.gmgn_debug_log = "logs/gmgn_debug.log"
        self.token_history = self._load_token_history()

        # Statistics tracking
        self.stats = {
            'messages_processed': 0,
            'tokens_extracted': 0,
            'duplicates_skipped': 0,
            'pnl_rechecks': 0,
            'alerts_by_type': {},
            'last_processed': None,
            'ca_frequency': {},  # Track how often each CA appears
            'source_frequency': {}  # Track message source patterns
        }

        # Setup debug logging
        self._setup_debug_logging()

        logger.info("GMGN Parser initialized with duplicate detection and PnL tracking")

    def _load_token_history(self) -> Dict:
        """Load token history for duplicate detection"""
        try:
            from utils.helpers import load_json_data
            return load_json_data(self.history_file, {})
        except Exception as e:
            logger.error(f"Failed to load token history: {e}")
            return {}

    def _setup_debug_logging(self):
        """Setup debug logging for GMGN parser"""
        try:
            import os
            os.makedirs("logs", exist_ok=True)

            # Create debug logger
            debug_logger = logging.getLogger('gmgn_debug')
            debug_logger.setLevel(logging.DEBUG)

            # Add file handler if not already present
            if not debug_logger.handlers:
                handler = logging.FileHandler(self.gmgn_debug_log)
                formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
                handler.setFormatter(formatter)
                debug_logger.addHandler(handler)

        except Exception as e:
            logger.error(f"Failed to setup debug logging: {e}")

    def _log_debug(self, message: str, data: Dict = None):
        """Log debug information"""
        try:
            debug_logger = logging.getLogger('gmgn_debug')
            if data:
                debug_logger.debug(f"{message}: {data}")
            else:
                debug_logger.debug(message)
        except Exception as e:
            logger.error(f"Debug logging failed: {e}")

    def _is_duplicate_ca(self, ca: str) -> bool:
        """Check if CA has been processed before"""
        return ca in self.token_history

    def _should_recheck_pnl(self, ca: str, current_alert_type: str) -> bool:
        """Determine if we should recheck PnL for a previously seen CA"""
        try:
            if ca not in self.token_history:
                return False

            token_data = self.token_history[ca]

            # Check if enough time has passed since last check
            last_check = token_data.get('last_pnl_check')
            if last_check:
                from datetime import datetime, timedelta
                last_check_time = datetime.fromisoformat(last_check)
                if datetime.now() - last_check_time < timedelta(hours=1):
                    return False

            # Check if this is a high-value alert type worth rechecking
            high_value_alerts = ['KOL FOMO', 'Smart Money', 'FDV Surge', 'Pump KOTH']
            if current_alert_type in high_value_alerts:
                return True

            # Check if we haven't hit our target multiplier yet
            peak_multiplier = token_data.get('peak_multiplier', 1.0)
            target_multiplier = self.config.get('gmgn_monitoring.target_multiplier', 2.0)

            return peak_multiplier < target_multiplier

        except Exception as e:
            logger.error(f"Error checking PnL recheck criteria: {e}")
            return False

    def _update_token_history(self, ca: str, alert_data: Dict):
        """Update token history with new alert data"""
        try:
            current_time = datetime.now().isoformat()

            if ca not in self.token_history:
                # New token
                self.token_history[ca] = {
                    'first_seen': current_time,
                    'last_triggered': current_time,
                    'seen_sources': [alert_data.get('alert_type', 'Unknown')],
                    'appeared_count': 1,
                    'peak_multiplier': 1.0,
                    'last_pnl_check': None,
                    'metadata_history': [alert_data.get('metadata', {})]
                }
            else:
                # Update existing token
                token_data = self.token_history[ca]
                token_data['last_triggered'] = current_time
                token_data['appeared_count'] = token_data.get('appeared_count', 0) + 1

                # Add new source if not already seen
                alert_type = alert_data.get('alert_type', 'Unknown')
                if alert_type not in token_data.get('seen_sources', []):
                    token_data['seen_sources'].append(alert_type)

                # Update metadata history
                if 'metadata_history' not in token_data:
                    token_data['metadata_history'] = []
                token_data['metadata_history'].append(alert_data.get('metadata', {}))

                # Keep only last 5 metadata entries
                if len(token_data['metadata_history']) > 5:
                    token_data['metadata_history'] = token_data['metadata_history'][-5:]

            # Save updated history
            from utils.helpers import save_json_data
            save_json_data(self.token_history, self.history_file)

        except Exception as e:
            logger.error(f"Failed to update token history: {e}")

    def parse_message(self, message: str) -> List[Dict]:
        """Parse a message with duplicate detection and PnL rechecking"""
        try:
            self.stats['messages_processed'] += 1
            self.stats['last_processed'] = datetime.now().isoformat()

            # Log raw message for debugging
            self._log_debug("Processing GMGN message", {"message_preview": message[:200]})

            # Check if this looks like a GMGN message
            if not is_gmgn_message(message):
                logger.debug("Message doesn't appear to be from GMGN")
                return []

            # Parse the message
            raw_results = gmgn_message_parser(message)
            processed_results = []

            for result in raw_results:
                ca = result.get('ca')
                alert_type = result.get('alert_type', 'Unknown')

                # Update frequency tracking
                self.stats['ca_frequency'][ca] = self.stats['ca_frequency'].get(ca, 0) + 1
                self.stats['source_frequency'][alert_type] = self.stats['source_frequency'].get(alert_type, 0) + 1

                # Check for duplicates
                if self._is_duplicate_ca(ca):
                    self.stats['duplicates_skipped'] += 1

                    # Check if we should recheck PnL
                    if self._should_recheck_pnl(ca, alert_type):
                        self.stats['pnl_rechecks'] += 1
                        result['is_pnl_recheck'] = True
                        result['recheck_reason'] = f"High-value alert: {alert_type}"

                        logger.info(f"PnL recheck triggered for {ca} due to {alert_type} alert")
                        self._log_debug("PnL recheck triggered", {
                            "ca": ca,
                            "alert_type": alert_type,
                            "appearance_count": self.stats['ca_frequency'][ca]
                        })

                        processed_results.append(result)
                    else:
                        logger.debug(f"Skipping duplicate CA: {ca} (seen {self.stats['ca_frequency'][ca]} times)")
                        self._log_debug("Duplicate CA skipped", {
                            "ca": ca,
                            "alert_type": alert_type,
                            "appearance_count": self.stats['ca_frequency'][ca]
                        })
                        continue
                else:
                    # New CA
                    result['is_new_ca'] = True
                    processed_results.append(result)
                    logger.info(f"New CA detected: {ca} via {alert_type}")

                # Update token history
                self._update_token_history(ca, result)

                # Update statistics
                alert_type = result.get('alert_type', 'Unknown')
                self.stats['alerts_by_type'][alert_type] = self.stats['alerts_by_type'].get(alert_type, 0) + 1

            # Log processing summary
            self._log_debug("Message processing complete", {
                "total_cas": len(raw_results),
                "processed_cas": len(processed_results),
                "duplicates_skipped": len(raw_results) - len(processed_results),
                "alert_types": [r.get('alert_type') for r in processed_results]
            })

            self.stats['tokens_extracted'] += len(processed_results)
            return processed_results

        except Exception as e:
            logger.error(f"Error in GMGN parser: {e}")
            self._log_debug("Parser error", {"error": str(e), "message_preview": message[:100]})
            return []
    
    def get_stats(self) -> Dict:
        """Get comprehensive parser statistics"""
        stats = self.stats.copy()

        # Add derived statistics
        stats['total_unique_cas'] = len(self.stats['ca_frequency'])
        stats['avg_ca_appearances'] = (
            sum(self.stats['ca_frequency'].values()) / len(self.stats['ca_frequency'])
            if self.stats['ca_frequency'] else 0
        )
        stats['most_frequent_ca'] = (
            max(self.stats['ca_frequency'].items(), key=lambda x: x[1])
            if self.stats['ca_frequency'] else None
        )
        stats['most_common_alert_type'] = (
            max(self.stats['alerts_by_type'].items(), key=lambda x: x[1])
            if self.stats['alerts_by_type'] else None
        )

        return stats

    def get_ca_reputation_score(self, ca: str) -> float:
        """Calculate reputation score for a CA based on appearance frequency and sources"""
        try:
            if ca not in self.token_history:
                return 1.0  # New CA gets neutral score

            token_data = self.token_history[ca]
            appearance_count = token_data.get('appeared_count', 1)
            seen_sources = token_data.get('seen_sources', [])

            # Base score decreases with excessive appearances (potential spam)
            if appearance_count > 10:
                base_score = 0.3
            elif appearance_count > 5:
                base_score = 0.6
            else:
                base_score = 1.0

            # Boost score for high-value sources
            high_value_sources = ['KOL FOMO', 'Smart Money', 'FDV Surge']
            value_boost = sum(0.1 for source in seen_sources if source in high_value_sources)

            return min(base_score + value_boost, 1.0)

        except Exception as e:
            logger.error(f"Error calculating reputation score: {e}")
            return 0.5

    def get_token_metadata_history(self, ca: str) -> List[Dict]:
        """Get metadata history for a specific token"""
        try:
            if ca in self.token_history:
                return self.token_history[ca].get('metadata_history', [])
            return []
        except Exception as e:
            logger.error(f"Error getting metadata history: {e}")
            return []

    def update_pnl_data(self, ca: str, current_price: float, peak_multiplier: float):
        """Update PnL data for a token"""
        try:
            if ca in self.token_history:
                self.token_history[ca]['last_pnl_check'] = datetime.now().isoformat()
                self.token_history[ca]['current_price'] = current_price
                self.token_history[ca]['peak_multiplier'] = max(
                    self.token_history[ca].get('peak_multiplier', 1.0),
                    peak_multiplier
                )

                # Save updated history
                from utils.helpers import save_json_data
                save_json_data(self.token_history, self.history_file)

                self._log_debug("PnL data updated", {
                    "ca": ca,
                    "current_price": current_price,
                    "peak_multiplier": peak_multiplier
                })

        except Exception as e:
            logger.error(f"Error updating PnL data: {e}")

    def cleanup_old_history(self, max_age_days: int = 30):
        """Clean up old token history entries"""
        try:
            from datetime import datetime, timedelta
            cutoff_time = datetime.now() - timedelta(days=max_age_days)

            tokens_to_remove = []
            for ca, token_data in self.token_history.items():
                first_seen = token_data.get('first_seen')
                if first_seen:
                    try:
                        first_seen_time = datetime.fromisoformat(first_seen)
                        if first_seen_time < cutoff_time:
                            tokens_to_remove.append(ca)
                    except:
                        # If we can't parse the date, remove it
                        tokens_to_remove.append(ca)

            for ca in tokens_to_remove:
                del self.token_history[ca]

            if tokens_to_remove:
                logger.info(f"Cleaned up {len(tokens_to_remove)} old token history entries")
                from utils.helpers import save_json_data
                save_json_data(self.token_history, self.history_file)

        except Exception as e:
            logger.error(f"Error cleaning up history: {e}")

    def reset_stats(self):
        """Reset parser statistics"""
        self.stats = {
            'messages_processed': 0,
            'tokens_extracted': 0,
            'duplicates_skipped': 0,
            'pnl_rechecks': 0,
            'alerts_by_type': {},
            'last_processed': None,
            'ca_frequency': {},
            'source_frequency': {}
        }

# Global parser instance
_gmgn_parser = None

def get_gmgn_parser() -> GMGNParser:
    """Get or create global GMGN parser instance"""
    global _gmgn_parser
    if _gmgn_parser is None:
        _gmgn_parser = GMGNParser()
    return _gmgn_parser
