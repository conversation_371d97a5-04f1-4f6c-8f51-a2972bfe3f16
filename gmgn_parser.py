"""
Monaco v1.0 - GMG<PERSON> Parser
Specialized parser for GMGN Signals messages with enhanced CA extraction and alert detection
"""

import re
import logging
from typing import Dict, List, Optional
from datetime import datetime
from utils.helpers import ConfigManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def is_valid_ca(address: str) -> bool:
    """Validate if string is a valid Solana contract address"""
    try:
        # Basic validation for Solana addresses
        if not address or len(address) < 32 or len(address) > 44:
            return False
        
        # Check if it contains only valid base58 characters
        valid_chars = set('123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz')
        if not all(c in valid_chars for c in address):
            return False
        
        # Blacklist common non-token addresses
        blacklisted = {
            'So11111111111111111111111111111111111111112',  # Wrapped SOL
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',  # USDT
            '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',  # RAY
        }
        
        return address not in blacklisted
        
    except Exception:
        return False

def extract_contract_addresses(message: str) -> List[str]:
    """Extract contract addresses from GMGN message"""
    try:
        # Extract from gmgn.ai token/address URLs
        ca_candidates = re.findall(r'https:\/\/gmgn\.ai\/sol\/(?:token|address)\/([1-9A-HJ-NP-Za-km-z]{32,44})', message)
        
        # Extract any raw CA-looking strings
        base_candidates = re.findall(r'\b([1-9A-HJ-NP-Za-km-z]{32,44})\b', message)
        
        # Combine and validate
        ca_set = set()
        for ca in ca_candidates + base_candidates:
            if is_valid_ca(ca):
                ca_set.add(ca)
        
        return list(ca_set)
        
    except Exception as e:
        logger.error(f"Error extracting contract addresses: {e}")
        return []

def detect_alert_type(message: str) -> str:
    """Detect the type of GMGN alert from message content"""
    try:
        message_lower = message.lower()
        
        # Check for specific GMGN alert patterns
        if "kol" in message_lower and "buy" in message_lower:
            return "KOL FOMO"
        
        if "smart money" in message_lower:
            return "Smart Money"
        
        if "fdv surge" in message_lower or "市值飙升" in message_lower:
            return "FDV Surge"
        
        if "advertised on dexscreener" in message_lower:
            return "DEX Ad"
        
        if "ct0" in message_lower or "cto" in message_lower:
            return "CTO Alert"
        
        if "update dexscreener" in message_lower:
            return "DEX Screener Social Update"
        
        if "pump已满" in message_lower or "pump completed" in message_lower:
            return "Pump Completed"
        
        if "king of the hill" in message_lower or "koth" in message_lower:
            return "Pump KOTH"
        
        # Additional patterns for common GMGN alerts
        if "new listing" in message_lower:
            return "New Listing"
        
        if "volume spike" in message_lower:
            return "Volume Spike"
        
        if "price surge" in message_lower:
            return "Price Surge"
        
        return "Unknown"
        
    except Exception as e:
        logger.error(f"Error detecting alert type: {e}")
        return "Unknown"

def parse_token_metadata(message: str) -> Dict:
    """Parse token metadata from GMGN message"""
    try:
        metadata = {}
        
        # Extract token symbol and name: $SYMBOL (Token Name)
        name_match = re.search(r'\$(\w+)\s*\(([^\)]+)\)', message)
        if name_match:
            metadata['symbol'] = name_match.group(1)
            metadata['name'] = name_match.group(2)
        
        # Extract market cap: MCP: $123.45K
        mcp_match = re.search(r'MCP: \$([\d.,]+[KMB]?)', message)
        if mcp_match:
            metadata['mcp'] = mcp_match.group(1)
        
        # Extract liquidity: Liq: ... ($123.45K)
        liq_match = re.search(r'Liq: .*?\(\$([\d.,KMB]+)', message)
        if liq_match:
            metadata['liquidity'] = liq_match.group(1)
        
        # Extract holder count: Holder: 123
        holders_match = re.search(r'Holder: (\d+)', message)
        if holders_match:
            metadata['holders'] = int(holders_match.group(1))
        
        # Extract open time: Open: 1.5h ago
        open_time_match = re.search(r'Open: ([\d.]+[hm]) ago', message)
        if open_time_match:
            metadata['open_time'] = open_time_match.group(1)
        
        # Extract price information if available
        price_match = re.search(r'Price: \$([\d.,]+)', message)
        if price_match:
            metadata['price'] = price_match.group(1)
        
        # Extract percentage changes
        change_match = re.search(r'([+-]?\d+\.?\d*%)', message)
        if change_match:
            metadata['price_change'] = change_match.group(1)
        
        return metadata
        
    except Exception as e:
        logger.error(f"Error parsing token metadata: {e}")
        return {}

def calculate_signal_strength(alert_type: str, metadata: Dict) -> float:
    """Calculate signal strength based on alert type and metadata"""
    try:
        base_scores = {
            "KOL FOMO": 0.8,
            "Smart Money": 0.9,
            "FDV Surge": 0.7,
            "DEX Ad": 0.6,
            "CTO Alert": 0.5,
            "Pump Completed": 0.4,
            "Pump KOTH": 0.8,
            "New Listing": 0.6,
            "Volume Spike": 0.7,
            "Price Surge": 0.7,
            "Unknown": 0.3
        }
        
        score = base_scores.get(alert_type, 0.3)
        
        # Boost score based on metadata quality
        if metadata.get('holders', 0) > 100:
            score += 0.1
        
        if metadata.get('mcp'):
            score += 0.05
        
        if metadata.get('liquidity'):
            score += 0.05
        
        # Cap at 1.0
        return min(score, 1.0)
        
    except Exception as e:
        logger.error(f"Error calculating signal strength: {e}")
        return 0.3

def gmgn_message_parser(raw_msg: str) -> List[Dict]:
    """
    Main GMGN message parser function
    Returns list of parsed token alerts from the message
    """
    try:
        # Extract contract addresses
        cas = extract_contract_addresses(raw_msg)
        
        if not cas:
            logger.debug("No valid contract addresses found in message")
            return []
        
        # Detect alert type
        alert_type = detect_alert_type(raw_msg)
        
        # Parse metadata
        metadata = parse_token_metadata(raw_msg)
        
        # Calculate signal strength
        signal_strength = calculate_signal_strength(alert_type, metadata)
        
        # Create timestamp
        timestamp = datetime.utcnow().isoformat()
        
        # Create result for each CA found
        results = []
        for ca in cas:
            result = {
                "ca": ca,
                "contract_address": ca,  # Alias for compatibility
                "alert_type": alert_type,
                "metadata": metadata,
                "signal_strength": signal_strength,
                "source": "GMGN",
                "timestamp": timestamp,
                "raw_message": raw_msg[:500],  # Truncate for storage
                "confidence": signal_strength  # Alias for compatibility
            }
            results.append(result)
            
        logger.info(f"GMGN parser extracted {len(results)} token alerts from message")
        return results
        
    except Exception as e:
        logger.error(f"Error in GMGN message parser: {e}")
        return []

def is_gmgn_message(message: str) -> bool:
    """Check if a message appears to be from GMGN based on patterns"""
    try:
        # Look for GMGN-specific indicators
        gmgn_indicators = [
            "gmgn.ai",
            "MCP:",
            "Holder:",
            "Open:",
            "kol buy",
            "smart money",
            "fdv surge",
            "pump已满",
            "king of the hill"
        ]
        
        message_lower = message.lower()
        return any(indicator.lower() in message_lower for indicator in gmgn_indicators)
        
    except Exception:
        return False

class GMGNParser:
    """Enhanced GMGN Parser class with configuration and statistics"""
    
    def __init__(self):
        self.config = ConfigManager()
        self.stats = {
            'messages_processed': 0,
            'tokens_extracted': 0,
            'alerts_by_type': {},
            'last_processed': None
        }
        
        logger.info("GMGN Parser initialized")
    
    def parse_message(self, message: str) -> List[Dict]:
        """Parse a message and update statistics"""
        try:
            self.stats['messages_processed'] += 1
            self.stats['last_processed'] = datetime.utcnow().isoformat()
            
            # Check if this looks like a GMGN message
            if not is_gmgn_message(message):
                logger.debug("Message doesn't appear to be from GMGN")
                return []
            
            # Parse the message
            results = gmgn_message_parser(message)
            
            # Update statistics
            self.stats['tokens_extracted'] += len(results)
            
            for result in results:
                alert_type = result.get('alert_type', 'Unknown')
                self.stats['alerts_by_type'][alert_type] = self.stats['alerts_by_type'].get(alert_type, 0) + 1
            
            return results
            
        except Exception as e:
            logger.error(f"Error in GMGN parser: {e}")
            return []
    
    def get_stats(self) -> Dict:
        """Get parser statistics"""
        return self.stats.copy()
    
    def reset_stats(self):
        """Reset parser statistics"""
        self.stats = {
            'messages_processed': 0,
            'tokens_extracted': 0,
            'alerts_by_type': {},
            'last_processed': None
        }

# Global parser instance
_gmgn_parser = None

def get_gmgn_parser() -> GMGNParser:
    """Get or create global GMGN parser instance"""
    global _gmgn_parser
    if _gmgn_parser is None:
        _gmgn_parser = GMGNParser()
    return _gmgn_parser
