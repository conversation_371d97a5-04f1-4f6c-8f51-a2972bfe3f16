"""
Monaco v1.0 - Launch Watcher
Monitors Solana blockchain for new token creation events and pump.fun launches
"""

import time
import logging
from typing import Dict, List, Set, Optional
from datetime import datetime, timedelta
import requests
from utils.helpers import (
    ConfigManager, SolanaRPCClient, DataProcessor, ErrorHandler,
    rate_limit, cached_request, save_json_data, load_json_data
)
from coin_analyzer import analyze_token
from autoposter import post_alert
from pnl_tracker import add_token_for_tracking

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TokenLaunchWatcher:
    """Main class for monitoring new token launches"""

    def __init__(self):
        self.config = ConfigManager()
        self.solana_client = SolanaRPCClient(self.config)
        self.data_processor = DataProcessor()
        self.error_handler = ErrorHandler(
            max_consecutive_errors=self.config.get('error_handling.max_consecutive_errors', 10)
        )

        # Known pump.fun program IDs and addresses
        self.pump_fun_program_id = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"  # Pump.fun program
        self.raydium_program_id = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"  # Raydium AMM

        # Tracking state
        self.processed_signatures: Set[str] = set()
        self.last_check_time = time.time()
        self.known_tokens: Dict[str, Dict] = load_json_data(
            self.config.get('storage.history_file', 'data/history.json'), {}
        )

        # Rate limiting
        self.poll_interval = self.config.get('monitoring.poll_interval_seconds', 10)
        self.max_concurrent = self.config.get('monitoring.max_concurrent_requests', 5)

        logger.info("TokenLaunchWatcher initialized")

    @rate_limit(max_calls=10, time_window=60)  # 10 calls per minute
    def get_pump_fun_tokens(self) -> List[Dict]:
        """Get recent tokens from pump.fun website (fallback method)"""
        try:
            # This is a fallback method using web scraping
            # In production, you might want to use official APIs
            url = "https://pump.fun/api/tokens/recent"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                return data.get('tokens', [])
            else:
                logger.warning(f"Pump.fun API returned status {response.status_code}")
                return []

        except Exception as e:
            logger.error(f"Failed to fetch pump.fun tokens: {e}")
            return []

    def monitor_solana_transactions(self) -> List[Dict]:
        """Monitor Solana blockchain for new token creation transactions"""
        try:
            new_tokens = []

            # Get recent signatures for pump.fun program
            signatures = self.solana_client.get_signatures_for_address(
                self.pump_fun_program_id,
                limit=50
            )

            for sig_info in signatures:
                signature = sig_info['signature']

                # Skip if already processed
                if signature in self.processed_signatures:
                    continue

                # Get transaction details
                transaction = self.solana_client.get_transaction(signature)
                if not transaction:
                    continue

                # Extract token information
                token_info = self.data_processor.extract_token_info(transaction)
                if token_info:
                    token_info['source'] = 'solana_rpc'
                    token_info['detected_at'] = datetime.now().isoformat()
                    new_tokens.append(token_info)
                    logger.info(f"Detected new token: {token_info.get('mint', 'unknown')}")

                # Mark as processed
                self.processed_signatures.add(signature)

            # Clean up old processed signatures (keep last 1000)
            if len(self.processed_signatures) > 1000:
                self.processed_signatures = set(list(self.processed_signatures)[-1000:])

            return new_tokens

        except Exception as e:
            if not self.error_handler.handle_error(e, "monitor_solana_transactions"):
                raise
            return []

    def process_new_token(self, token_data: Dict) -> bool:
        """Process a newly detected token"""
        try:
            token_address = token_data.get('mint') or token_data.get('address')
            if not token_address:
                logger.warning("Token data missing address")
                return False

            # Skip if already processed
            if token_address in self.known_tokens:
                return False

            # Analyze the token
            analysis_result = analyze_token(token_data)

            if analysis_result.get('is_promising', False):
                # Post alert to Telegram
                alert_sent = post_alert(token_data, analysis_result)

                if alert_sent:
                    logger.info(f"Alert sent for token: {token_address}")
                    token_data['alert_sent'] = True
                    token_data['alert_sent_at'] = datetime.now().isoformat()

                # Add to PnL tracking for promising tokens
                try:
                    add_token_for_tracking(token_address, analysis_result)
                    logger.info(f"Added {token_address} to PnL tracking")
                except Exception as e:
                    logger.error(f"Failed to add token to PnL tracking: {e}")

            # Store token data
            self.known_tokens[token_address] = {
                **token_data,
                'analysis': analysis_result,
                'processed_at': datetime.now().isoformat()
            }

            # Save to file periodically
            if len(self.known_tokens) % 10 == 0:
                self.save_token_data()

            return True

        except Exception as e:
            self.error_handler.handle_error(e, f"process_new_token: {token_data}")
            return False

    def save_token_data(self):
        """Save token data to persistent storage"""
        try:
            filepath = self.config.get('storage.history_file', 'data/history.json')
            save_json_data(self.known_tokens, filepath)
            logger.debug(f"Saved {len(self.known_tokens)} tokens to {filepath}")
        except Exception as e:
            logger.error(f"Failed to save token data: {e}")

    def cleanup_old_data(self):
        """Remove old token data to prevent memory bloat"""
        try:
            max_age_days = self.config.get('storage.max_history_days', 30)
            cutoff_time = datetime.now() - timedelta(days=max_age_days)

            tokens_to_remove = []
            for token_address, token_data in self.known_tokens.items():
                processed_at = token_data.get('processed_at')
                if processed_at:
                    try:
                        processed_time = datetime.fromisoformat(processed_at.replace('Z', '+00:00'))
                        if processed_time < cutoff_time:
                            tokens_to_remove.append(token_address)
                    except:
                        # If we can't parse the date, remove it
                        tokens_to_remove.append(token_address)

            for token_address in tokens_to_remove:
                del self.known_tokens[token_address]

            if tokens_to_remove:
                logger.info(f"Cleaned up {len(tokens_to_remove)} old token records")
                self.save_token_data()

        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")

def watch_loop():
    """Main monitoring loop"""
    watcher = TokenLaunchWatcher()

    logger.info("Starting Monaco v1.0 token launch monitoring...")

    try:
        while watcher.error_handler.should_continue():
            try:
                # Monitor for new tokens
                new_tokens = []

                # Method 1: Monitor Solana blockchain directly
                solana_tokens = watcher.monitor_solana_transactions()
                new_tokens.extend(solana_tokens)

                # Method 2: Fallback to pump.fun API/scraping
                if not solana_tokens:  # Only use fallback if primary method fails
                    pump_tokens = watcher.get_pump_fun_tokens()
                    new_tokens.extend(pump_tokens)

                # Process each new token
                for token_data in new_tokens:
                    watcher.process_new_token(token_data)

                # Reset error count on successful iteration
                if new_tokens:
                    watcher.error_handler.reset_error_count()

                # Periodic cleanup
                if time.time() - watcher.last_check_time > 3600:  # Every hour
                    watcher.cleanup_old_data()
                    watcher.last_check_time = time.time()

                # Status logging
                if len(new_tokens) > 0:
                    logger.info(f"Processed {len(new_tokens)} new tokens")
                else:
                    logger.debug("No new tokens detected")

                # Wait before next iteration
                time.sleep(watcher.poll_interval)

            except KeyboardInterrupt:
                logger.info("Received interrupt signal, shutting down...")
                break
            except Exception as e:
                if not watcher.error_handler.handle_error(e, "main_loop"):
                    break

                # Wait longer after errors
                time.sleep(watcher.poll_interval * 2)

    finally:
        # Save final state
        watcher.save_token_data()
        logger.info("Monaco v1.0 monitoring stopped")