"""
Monaco v1.0 - Launch Watcher
Monitors Solana blockchain and Telegram signals for new token detection
"""

import time
import logging
import asyncio
from typing import Dict, List, Set, Optional
from datetime import datetime, timedelta
from utils.helpers import (
    ConfigManager, SolanaRPCClient, DataProcessor, ErrorHandler,
    rate_limit, save_json_data, load_json_data
)
from coin_analyzer import analyze_token
from autoposter import post_alert
from pnl_tracker import add_token_for_tracking
from auth.session_manager import get_session_manager
from gmgn_parser import get_gmgn_parser
from dex_preview import generate_token_preview

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TokenLaunchWatcher:
    """Main class for monitoring new token launches via Solana RPC and Telegram signals"""

    def __init__(self):
        self.config = ConfigManager()
        self.solana_client = SolanaRPCClient(self.config)
        self.data_processor = DataProcessor()
        self.error_handler = ErrorHandler(
            max_consecutive_errors=self.config.get('error_handling.max_consecutive_errors', 10)
        )

        # Telegram session manager
        self.session_manager = get_session_manager()

        # Known program IDs for token creation
        self.token_program_id = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"  # SPL Token Program
        self.raydium_program_id = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"  # Raydium AMM

        # Tracking state
        self.processed_signatures: Set[str] = set()
        self.processed_telegram_messages: Set[int] = set()
        self.telegram_detected_tokens: Dict[str, Dict] = {}
        self.last_check_time = time.time()
        self.known_tokens: Dict[str, Dict] = load_json_data(
            self.config.get('storage.history_file', 'data/history.json'), {}
        )

        # Operation mode
        self.operation_mode = self.config.get('operation_mode.current_mode', 'test')
        self.is_test_mode = self.operation_mode == 'test'

        # Rate limiting
        self.poll_interval = self.config.get('monitoring.poll_interval_seconds', 10)
        self.max_concurrent = self.config.get('monitoring.max_concurrent_requests', 5)

        # GMGN monitoring settings
        self.gmgn_enabled = self.config.get('gmgn_monitoring.enabled', True)
        self.gmgn_group = self.config.get('gmgn_monitoring.group_identifier', 'gmgn_signals')

        logger.info(f"TokenLaunchWatcher initialized in {self.operation_mode.upper()} mode")
        logger.info(f"GMGN monitoring: {'ENABLED' if self.gmgn_enabled else 'DISABLED'}")

    async def handle_telegram_message(self, message_data: Dict):
        """Handle incoming Telegram messages from GMGN group"""
        try:
            message_id = message_data.get('id')
            message_text = message_data.get('text', '')

            # Skip if already processed or empty message
            if message_id in self.processed_telegram_messages or not message_text:
                return

            # Use GMGN parser to process the message
            gmgn_parser = get_gmgn_parser()
            parsed_alerts = gmgn_parser.parse_message(message_text)

            if parsed_alerts:
                logger.info(f"GMGN signal detected: {len(parsed_alerts)} token alerts from message {message_id}")

                # Process each detected token alert
                for alert in parsed_alerts:
                    ca = alert.get('ca') or alert.get('contract_address')
                    alert_type = alert.get('alert_type', 'Unknown')
                    signal_strength = alert.get('signal_strength', 0)
                    is_new_ca = alert.get('is_new_ca', False)
                    is_pnl_recheck = alert.get('is_pnl_recheck', False)

                    if ca:
                        # Handle new CAs
                        if is_new_ca and ca not in self.telegram_detected_tokens:
                            # Store GMGN detection data
                            self.telegram_detected_tokens[ca] = {
                                'detected_via': 'gmgn_telegram',
                                'source_message': alert,
                                'detection_time': datetime.now().isoformat(),
                                'signal_strength': signal_strength,
                                'alert_type': alert_type,
                                'metadata': alert.get('metadata', {}),
                                'message_id': message_id,
                                'is_new_ca': True
                            }

                            logger.info(f"Processing NEW GMGN alert: {alert_type} for token {ca} (strength: {signal_strength:.2f})")

                            # Process the token through the pipeline
                            await self.process_telegram_detected_token(ca, alert)

                        # Handle PnL rechecks
                        elif is_pnl_recheck:
                            logger.info(f"Processing GMGN PnL recheck: {alert_type} for token {ca} (strength: {signal_strength:.2f})")

                            # Trigger PnL recheck
                            await self.handle_pnl_recheck(ca, alert)

                        else:
                            logger.debug(f"Skipping already processed CA: {ca}")
            else:
                logger.debug(f"No GMGN alerts found in message {message_id}")

            # Mark message as processed
            self.processed_telegram_messages.add(message_id)

            # Clean up old processed messages (keep last 1000)
            if len(self.processed_telegram_messages) > 1000:
                self.processed_telegram_messages = set(list(self.processed_telegram_messages)[-1000:])

        except Exception as e:
            logger.error(f"Error handling Telegram message: {e}")

    async def process_telegram_detected_token(self, token_address: str, gmgn_alert: Dict):
        """Process a token detected via GMGN Telegram signals"""
        try:
            # Create token data structure with GMGN-specific information
            token_data = {
                'address': token_address,
                'mint': token_address,
                'source': 'gmgn_telegram',
                'detected_at': datetime.now().isoformat(),
                'gmgn_alert': gmgn_alert,
                'alert_type': gmgn_alert.get('alert_type', 'Unknown'),
                'signal_strength': gmgn_alert.get('signal_strength', 0),
                'metadata': gmgn_alert.get('metadata', {}),
                'confidence': gmgn_alert.get('confidence', 0)
            }

            # Add token metadata from GMGN if available
            metadata = gmgn_alert.get('metadata', {})
            if metadata:
                token_data.update({
                    'symbol': metadata.get('symbol'),
                    'name': metadata.get('name'),
                    'holders': metadata.get('holders'),
                    'mcp': metadata.get('mcp'),
                    'liquidity': metadata.get('liquidity'),
                    'open_time': metadata.get('open_time'),
                    'price': metadata.get('price'),
                    'price_change': metadata.get('price_change')
                })

            # Cross-reference with blockchain if enabled
            if self.config.get('signal_validation.cross_reference_blockchain', True):
                blockchain_data = await self.verify_token_on_blockchain(token_address)
                if blockchain_data:
                    token_data.update(blockchain_data)
                    token_data['cross_referenced'] = True
                    logger.info(f"GMGN signal cross-referenced with blockchain data for {token_address}")
                else:
                    token_data['cross_referenced'] = False
                    logger.warning(f"Could not cross-reference GMGN signal with blockchain for {token_address}")

            # Process the token through normal pipeline
            success = self.process_new_token(token_data)

            if success:
                alert_type = gmgn_alert.get('alert_type', 'Unknown')
                signal_strength = gmgn_alert.get('signal_strength', 0)
                logger.info(f"Successfully processed GMGN-detected token: {token_address} "
                           f"(Type: {alert_type}, Strength: {signal_strength:.2f})")

        except Exception as e:
            logger.error(f"Error processing GMGN-detected token {token_address}: {e}")

    async def handle_pnl_recheck(self, token_address: str, gmgn_alert: Dict):
        """Handle PnL recheck for previously seen tokens"""
        try:
            from pnl_tracker import get_tracker
            from coin_analyzer import analyze_token

            logger.info(f"Performing PnL recheck for {token_address}")

            # Get current PnL data
            tracker = get_tracker()
            performance_data = tracker.update_token_performance(token_address)

            if performance_data:
                current_multiplier = performance_data.get('price_change_multiplier', 1.0)
                target_multiplier = self.config.get('gmgn_monitoring.target_multiplier', 2.0)

                logger.info(f"PnL recheck for {token_address}: {current_multiplier:.2f}x "
                           f"(target: {target_multiplier:.2f}x)")

                # Update GMGN parser with PnL data
                gmgn_parser = get_gmgn_parser()
                gmgn_parser.update_pnl_data(
                    token_address,
                    performance_data.get('current_price', 0),
                    current_multiplier
                )

                # Check if we've hit the target multiplier
                if current_multiplier >= target_multiplier:
                    logger.info(f"🎯 Target multiplier reached for {token_address}: {current_multiplier:.2f}x")

                    # Create token data for re-alerting
                    token_data = {
                        'address': token_address,
                        'mint': token_address,
                        'source': 'gmgn_pnl_recheck',
                        'detected_at': datetime.now().isoformat(),
                        'gmgn_alert': gmgn_alert,
                        'pnl_data': performance_data,
                        'multiplier_achieved': current_multiplier,
                        'recheck_reason': gmgn_alert.get('recheck_reason', 'PnL target reached')
                    }

                    # Re-analyze and potentially re-alert
                    analysis_result = analyze_token(token_data)
                    analysis_result['is_pnl_success'] = True
                    analysis_result['multiplier_achieved'] = current_multiplier

                    # Post success alert if in live mode
                    if not self.is_test_mode:
                        from autoposter import post_alert
                        alert_sent = post_alert(token_data, analysis_result)
                        if alert_sent:
                            logger.info(f"PnL success alert sent for {token_address}")
                    else:
                        logger.info(f"Test mode: Would have sent PnL success alert for {token_address}")

                else:
                    logger.debug(f"Target multiplier not yet reached for {token_address}: "
                                f"{current_multiplier:.2f}x < {target_multiplier:.2f}x")

            else:
                logger.warning(f"No performance data available for PnL recheck: {token_address}")

        except Exception as e:
            logger.error(f"Error handling PnL recheck for {token_address}: {e}")

    async def verify_token_on_blockchain(self, token_address: str) -> Optional[Dict]:
        """Verify token exists on Solana blockchain"""
        try:
            # Get recent transactions for the token address
            signatures = self.solana_client.get_signatures_for_address(token_address, limit=10)

            if signatures:
                # Get the most recent transaction
                latest_tx = self.solana_client.get_transaction(signatures[0]['signature'])

                if latest_tx:
                    token_info = self.data_processor.extract_token_info(latest_tx)
                    if token_info:
                        return {
                            'blockchain_verified': True,
                            'blockchain_data': token_info,
                            'verification_time': datetime.now().isoformat()
                        }

            return {
                'blockchain_verified': False,
                'verification_time': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error verifying token on blockchain: {e}")
            return None

    def monitor_solana_transactions(self) -> List[Dict]:
        """Monitor Solana blockchain for new token creation transactions"""
        try:
            new_tokens = []

            # Monitor SPL Token Program for new token mints
            signatures = self.solana_client.get_signatures_for_address(
                self.token_program_id,
                limit=50
            )

            for sig_info in signatures:
                signature = sig_info['signature']

                # Skip if already processed
                if signature in self.processed_signatures:
                    continue

                # Get transaction details
                transaction = self.solana_client.get_transaction(signature)
                if not transaction:
                    continue

                # Extract token information
                token_info = self.data_processor.extract_token_info(transaction)
                if token_info:
                    token_info['source'] = 'solana_rpc'
                    token_info['detected_at'] = datetime.now().isoformat()
                    new_tokens.append(token_info)
                    logger.info(f"Detected new token via blockchain: {token_info.get('mint', 'unknown')}")

                # Mark as processed
                self.processed_signatures.add(signature)

            # Clean up old processed signatures (keep last 1000)
            if len(self.processed_signatures) > 1000:
                self.processed_signatures = set(list(self.processed_signatures)[-1000:])

            return new_tokens

        except Exception as e:
            if not self.error_handler.handle_error(e, "monitor_solana_transactions"):
                raise
            return []

    def process_new_token(self, token_data: Dict) -> bool:
        """Process a newly detected token"""
        try:
            token_address = token_data.get('mint') or token_data.get('address')
            if not token_address:
                logger.warning("Token data missing address")
                return False

            # Skip if already processed
            if token_address in self.known_tokens:
                return False

            # Analyze the token
            analysis_result = analyze_token(token_data)

            # Calculate combined signal score if from multiple sources
            combined_score = self.calculate_combined_signal_score(token_data, analysis_result)
            analysis_result['combined_score'] = combined_score

            # Check if token meets criteria for alerting
            should_alert = self.should_send_alert(analysis_result, combined_score)

            if should_alert:
                # Generate preview materials if enabled
                preview_data = None
                if self.config.get('dex_preview.enabled', True):
                    try:
                        preview_data = generate_token_preview(token_address, analysis_result)
                        if preview_data:
                            analysis_result['preview_data'] = preview_data
                    except Exception as e:
                        logger.warning(f"Failed to generate preview for {token_address}: {e}")

                # Post alert to Telegram (only in live mode or if test mode allows)
                should_post = not self.is_test_mode or self.config.get('operation_mode.test_mode.post_alerts', False)

                if should_post:
                    alert_sent = post_alert(token_data, analysis_result)

                    if alert_sent:
                        logger.info(f"Alert sent for token: {token_address}")
                        token_data['alert_sent'] = True
                        token_data['alert_sent_at'] = datetime.now().isoformat()

                        # Add to PnL tracking for promising tokens
                        try:
                            add_token_for_tracking(token_address, analysis_result)
                            logger.info(f"Added {token_address} to PnL tracking")
                        except Exception as e:
                            logger.error(f"Failed to add token to PnL tracking: {e}")
                else:
                    logger.info(f"Test mode: Would have sent alert for {token_address}")
                    token_data['would_have_alerted'] = True

            # Store token data
            self.known_tokens[token_address] = {
                **token_data,
                'analysis': analysis_result,
                'processed_at': datetime.now().isoformat(),
                'operation_mode': self.operation_mode
            }

            # Save to file periodically
            if len(self.known_tokens) % 10 == 0:
                self.save_token_data()

            return True

        except Exception as e:
            self.error_handler.handle_error(e, f"process_new_token: {token_data}")
            return False

    def calculate_combined_signal_score(self, token_data: Dict, analysis_result: Dict) -> float:
        """Calculate combined signal score from multiple sources"""
        try:
            score = 0.0
            weights = self.config.get('signal_validation.scoring', {})

            # GMGN Telegram signal weight
            if token_data.get('source') == 'gmgn_telegram':
                telegram_weight = weights.get('telegram_signal_weight', 0.4)
                signal_strength = token_data.get('signal_strength', 0)
                score += telegram_weight * signal_strength

                # Bonus for high-value GMGN alert types
                alert_type = token_data.get('alert_type', '')
                high_value_alerts = ['KOL FOMO', 'Smart Money', 'FDV Surge', 'Pump KOTH']
                if alert_type in high_value_alerts:
                    score += 0.1

                # Bonus for metadata quality
                metadata = token_data.get('metadata', {})
                if metadata.get('holders', 0) > 50:
                    score += 0.05
                if metadata.get('mcp'):
                    score += 0.05

            # Blockchain detection weight
            if token_data.get('source') == 'solana_rpc' or token_data.get('cross_referenced', False):
                blockchain_weight = weights.get('blockchain_detection_weight', 0.6)
                score += blockchain_weight

            # Analysis confidence from coin analyzer
            recommendation = analysis_result.get('recommendation', {})
            confidence_map = {'HIGH': 1.0, 'MEDIUM': 0.7, 'LOW': 0.4}
            confidence_score = confidence_map.get(recommendation.get('confidence', 'LOW'), 0.4)
            score += weights.get('trigger_confidence_weight', 0.3) * confidence_score

            # Additional scoring for GMGN-specific factors
            if token_data.get('source') == 'gmgn_telegram':
                # Boost score if we have cross-reference validation
                if token_data.get('cross_referenced', False):
                    score += 0.15

                # Time-based scoring (newer tokens get higher scores)
                open_time = token_data.get('open_time', '')
                if 'h' in open_time:
                    try:
                        hours = float(open_time.replace('h', ''))
                        if hours < 1:  # Less than 1 hour old
                            score += 0.1
                        elif hours < 6:  # Less than 6 hours old
                            score += 0.05
                    except:
                        pass

            return min(score, 1.0)

        except Exception as e:
            logger.error(f"Error calculating combined score: {e}")
            return 0.5

    def should_send_alert(self, analysis_result: Dict, combined_score: float) -> bool:
        """Determine if an alert should be sent based on analysis and configuration"""
        try:
            # Check if token is promising from analysis
            if not analysis_result.get('is_promising', False):
                return False

            # Check combined score threshold
            min_score = self.config.get('signal_validation.min_combined_score', 0.7)
            if combined_score < min_score:
                logger.debug(f"Combined score {combined_score:.2f} below threshold {min_score}")
                return False

            # Check if dual confirmation is required
            require_dual = self.config.get('signal_validation.require_dual_confirmation', False)
            if require_dual:
                # Would need both Telegram signal AND blockchain detection
                # This logic would be implemented based on specific requirements
                pass

            return True

        except Exception as e:
            logger.error(f"Error determining alert eligibility: {e}")
            return False

    def save_token_data(self):
        """Save token data to persistent storage"""
        try:
            filepath = self.config.get('storage.history_file', 'data/history.json')
            save_json_data(self.known_tokens, filepath)
            logger.debug(f"Saved {len(self.known_tokens)} tokens to {filepath}")
        except Exception as e:
            logger.error(f"Failed to save token data: {e}")

    def cleanup_old_data(self):
        """Remove old token data to prevent memory bloat"""
        try:
            max_age_days = self.config.get('storage.max_history_days', 30)
            cutoff_time = datetime.now() - timedelta(days=max_age_days)

            tokens_to_remove = []
            for token_address, token_data in self.known_tokens.items():
                processed_at = token_data.get('processed_at')
                if processed_at:
                    try:
                        processed_time = datetime.fromisoformat(processed_at.replace('Z', '+00:00'))
                        if processed_time < cutoff_time:
                            tokens_to_remove.append(token_address)
                    except:
                        # If we can't parse the date, remove it
                        tokens_to_remove.append(token_address)

            for token_address in tokens_to_remove:
                del self.known_tokens[token_address]

            if tokens_to_remove:
                logger.info(f"Cleaned up {len(tokens_to_remove)} old token records")
                self.save_token_data()

        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")

async def setup_telegram_monitoring(watcher: TokenLaunchWatcher):
    """Setup Telegram monitoring for GMGN signals"""
    try:
        if not watcher.gmgn_enabled:
            logger.info("GMGN monitoring disabled in configuration")
            return False

        # Initialize Telegram session
        session_manager = watcher.session_manager
        success = await session_manager.initialize_session()

        if not success:
            logger.error("Failed to initialize Telegram session")
            return False

        # Add message handler for GMGN group
        await session_manager.add_message_handler(
            watcher.gmgn_group,
            watcher.handle_telegram_message
        )

        # Get group info
        group_info = await session_manager.get_group_info(watcher.gmgn_group)
        if group_info:
            logger.info(f"Monitoring Telegram group: {group_info.get('title', watcher.gmgn_group)}")
        else:
            logger.warning(f"Could not get info for group: {watcher.gmgn_group}")

        return True

    except Exception as e:
        logger.error(f"Failed to setup Telegram monitoring: {e}")
        return False

async def async_watch_loop():
    """Async main monitoring loop with Telegram integration"""
    watcher = TokenLaunchWatcher()

    logger.info(f"Starting Monaco v1.0 token monitoring in {watcher.operation_mode.upper()} mode...")

    # Setup Telegram monitoring
    telegram_setup = await setup_telegram_monitoring(watcher)
    if telegram_setup:
        logger.info("Telegram monitoring initialized successfully")
    else:
        logger.warning("Continuing without Telegram monitoring")

    try:
        while watcher.error_handler.should_continue():
            try:
                # Monitor for new tokens via Solana blockchain
                solana_tokens = watcher.monitor_solana_transactions()

                # Process each new token from blockchain
                for token_data in solana_tokens:
                    watcher.process_new_token(token_data)

                # Reset error count on successful iteration
                if solana_tokens:
                    watcher.error_handler.reset_error_count()

                # Periodic cleanup
                if time.time() - watcher.last_check_time > 3600:  # Every hour
                    watcher.cleanup_old_data()
                    watcher.last_check_time = time.time()

                # Status logging
                blockchain_count = len(solana_tokens)
                telegram_count = len(watcher.telegram_detected_tokens)

                if blockchain_count > 0 or telegram_count > 0:
                    logger.info(f"Processed {blockchain_count} blockchain tokens, "
                               f"tracking {telegram_count} Telegram signals")
                else:
                    logger.debug("No new tokens detected")

                # Wait before next iteration
                await asyncio.sleep(watcher.poll_interval)

            except KeyboardInterrupt:
                logger.info("Received interrupt signal, shutting down...")
                break
            except Exception as e:
                if not watcher.error_handler.handle_error(e, "main_loop"):
                    break

                # Wait longer after errors
                await asyncio.sleep(watcher.poll_interval * 2)

    finally:
        # Disconnect Telegram session
        if telegram_setup:
            await watcher.session_manager.disconnect()

        # Save final state
        watcher.save_token_data()
        logger.info("Monaco v1.0 monitoring stopped")

def watch_loop():
    """Main monitoring loop - wrapper for async functionality"""
    try:
        # Run the async loop
        asyncio.run(async_watch_loop())
    except Exception as e:
        logger.error(f"Critical error in watch loop: {e}")
        raise